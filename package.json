{"name": "next-js-boilerplate", "version": "3.9.0", "scripts": {"dev": "next dev -p 8081", "build": "next build", "start": "next start", "build-stats": "cross-env ANALYZE=true npm run build", "export": "next export", "build-prod": "run-s clean build export", "clean": "rimraf .next out", "lint": "next lint", "format": "next lint --fix && prettier '**/*.{json,yaml}' --write --ignore-path .gitignore", "check-types": "tsc --noEmit --pretty && tsc --project cypress --noEmit --pretty", "test": "jest", "commit": "cz", "cypress": "cypress open", "cypress:headless": "cypress run", "e2e": "start-server-and-test dev http://localhost:3000 cypress", "e2e:headless": "start-server-and-test dev http://localhost:3000 cypress:headless", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "storybook:serve": "http-server storybook-static --port 6006 --silent", "serve-storybook": "run-s storybook:*", "test-storybook:ci": "start-server-and-test serve-storybook http://localhost:6006 test-storybook", "prepare": "husky install", "postbuild": "next-sitemap"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@reduxjs/toolkit": "^1.9.5", "@types/react-scroll": "^1.8.6", "@types/react-slick": "^0.23.10", "axios": "^1.4.0", "classnames": "^2.3.2", "date-fns": "^2.30.0", "dayjs": "^1.11.10", "next": "14.0.0", "next-seo": "^5.15.0", "next-sitemap": "^4.0.6", "next-themes": "^0.2.1", "qs": "^6.11.2", "rc-slider": "^10.1.1", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-datepicker": "^4.21.0", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-inlinesvg": "^3.0.2", "react-player": "^2.12.0", "react-redux": "^8.0.5", "react-scroll": "^1.8.9", "react-scroll-parallax": "^3.4.2", "react-select": "^5.8.0", "react-share": "^5.1.0", "react-slick": "^0.29.0", "react-snaplist-carousel": "^4.4.2", "react-tiny-popover": "^8.0.4", "react-toastify": "^9.1.3", "react-use-scroll-snap": "^0.0.4", "react-youtube": "^10.1.0", "sass": "^1.62.0", "slick-carousel": "^1.8.1", "swiper": "^8.4.7", "tailwind-scrollbar-hide": "^1.1.7", "yet-another-react-lightbox": "^3.10.0", "yup": "^1.3.2"}, "devDependencies": {"@commitlint/cli": "^17.5.1", "@commitlint/config-conventional": "^17.4.4", "@commitlint/cz-commitlint": "^17.5.0", "@next/bundle-analyzer": "^13.3.0", "@percy/cli": "^1.22.0", "@percy/cypress": "^3.1.2", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@storybook/addon-essentials": "^7.0.2", "@storybook/addon-interactions": "^7.0.2", "@storybook/addon-links": "^7.0.2", "@storybook/blocks": "^7.0.2", "@storybook/nextjs": "^7.0.2", "@storybook/react": "^7.0.2", "@storybook/test-runner": "^0.10.0", "@storybook/testing-library": "^0.0.14-next.2", "@testing-library/cypress": "^10.0.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@types/jest": "^29.5.0", "@types/node": "^18.15.11", "@types/react": "^18.0.33", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-datepicker": "^4.19.3", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "autoprefixer": "^10.4.14", "commitizen": "^4.3.0", "cross-env": "^7.0.3", "cssnano": "^6.0.0", "cypress": "^13.5.1", "eslint": "^8.37.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-next": "^13.3.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-cypress": "^2.13.2", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-jest-dom": "^4.0.3", "eslint-plugin-jest-formatting": "^3.1.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "5.0.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-storybook": "^0.6.11", "eslint-plugin-tailwindcss": "^3.11.0", "eslint-plugin-testing-library": "^5.10.2", "eslint-plugin-unused-imports": "^2.0.0", "http-server": "^14.1.1", "husky": "^8.0.3", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "lint-staged": "^13.2.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.21", "prettier": "^3.1.0", "rimraf": "^4.4.1", "semantic-release": "^22.0.8", "start-server-and-test": "^2.0.0", "storybook": "^7.0.2", "tailwindcss": "^3.4.3", "typescript": "^4.9.5"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "release": {"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/npm", {"npmPublish": false}], "@semantic-release/git", "@semantic-release/github"]}, "overrides": {"@swc/core": {"version": "1.12.5"}}, "author": "Ixartz (https://github.com/ixartz)"}