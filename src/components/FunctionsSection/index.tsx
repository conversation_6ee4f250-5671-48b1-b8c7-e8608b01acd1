import { useRouter } from 'next/router';
import React, { useEffect, useRef, useState } from 'react';

import AppImage from '@/components/AppImage';
import Icon from '@/components/Icon';
import Title from '@/components/Title';
import { sectionClassName } from '@/constants';
import type { ILanguageInput } from '@/typings/common';
import { formatDescription } from '@/utils';

interface IProps {
  subTitle?: string;
  title: string;
  data?: any;

  url?: string;
  className?: string;
}

const FunctionsSection = ({
  subTitle,
  title,
  data,
  url,
  className,
}: IProps) => {
  const horizontalScrollRef = useRef(null);
  const locale = useRouter().locale as keyof ILanguageInput;
  const [functionSelected, setFunctionSelected] = useState(data[0]);

  useEffect(() => {
    setTimeout(() => {
      const element: any = document.getElementsByClassName('item-active')[0];
      if (!element?.offsetLeft || !element?.offsetWidth) return;
      // @ts-ignore
      horizontalScrollRef?.current?.scrollTo({
        left:
          element.offsetLeft - (window.innerWidth - element.offsetWidth) / 2,
        behavior: 'smooth',
      });
    }, 0);
  }, [functionSelected]);

  return (
    <section
      className={`${sectionClassName} max-w-[1600px] lg:px-0 ${className}`}
    >
      <div className={'space-y-6 text-center md:px-4 md:text-left'}>
        <h5 className={`barlow text-18 text-red underline md:text-14`}>
          <b>{subTitle}</b>
        </h5>
        <Title text={title} />
      </div>
      <div
        className={`mx-auto my-[50px] grid items-center gap-x-20 lg:gap-x-6 lg:px-4 ${
          url === 'principals-management'
            ? 'grid-cols-[60%_auto] md:grid-cols-1'
            : 'max-w-[1034px] grid-cols-[420px_auto] md:grid-cols-1'
        }`}
      >
        <div className={'min-h-[400px] md:min-h-[360px]'}>
          <AppImage
            src={`/assets/images/${url}/functions/${functionSelected?.id}.png`}
            className={'!object-contain'}
          />
        </div>
        <div className={'space-y-6 md:space-y-2'}>
          <h3 className={'text-32 md:text-18'}>
            {functionSelected?.title[locale]}
          </h3>
          <p
            dangerouslySetInnerHTML={{
              __html: formatDescription(
                functionSelected?.description[locale] || '',
              ),
            }}
          />
        </div>
      </div>
      <ul
        ref={horizontalScrollRef}
        className={
          'hide-scrollbar flex justify-between gap-x-[80px] lg:gap-x-8 lg:overflow-x-auto lg:px-4'
        }
      >
        {data.map((item: any) => {
          const isSelected = item.id === functionSelected?.id;
          return (
            <li
              key={item.id}
              onClick={() => {
                setFunctionSelected(item);
              }}
              className={
                'flex cursor-pointer flex-col items-center justify-center gap-y-6'
              }
            >
              <div
                className={`flex aspect-square w-[112px] items-center justify-center rounded-full md:w-16 ${
                  isSelected ? 'item-active bg-blue' : 'bg-blue/10'
                }`}
              >
                <Icon
                  name={item.icon}
                  className={`h-12 w-12 md:h-8 md:w-8 ${
                    isSelected ? 'fill-white' : 'fill-blue'
                  }`}
                />
              </div>
              <p className={'barlow--bold text-center md:hidden'}>
                {item.subTitle[locale]}
              </p>
            </li>
          );
        })}
      </ul>
      <div className={'mt-4 hidden md:block'}>
        <p className={'text-center font-bold'}>
          {functionSelected?.subTitle[locale]}
        </p>
      </div>
    </section>
  );
};

export default FunctionsSection;
