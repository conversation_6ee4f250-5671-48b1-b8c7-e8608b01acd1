import React from 'react';

import TextError from '@/components/TextError';

interface IProps {
  label?: string;
  placeholder?: string;
  error?: string;
  type?: string;
  value: any;
  required?: boolean;
  className?: string;
  inputClassName?: string;
  prefix?: string;
  onChange?: any;
}

const Input = ({
  label,
  placeholder,
  error = '',
  type = 'text',
  value,
  required = true,
  className = '',
  inputClassName = '',
  prefix = '',
  onChange,
}: IProps) => {
  return (
    <div className={`w-full ${className}`}>
      {label && (
        <h4 className={'mb-2 text-blue'}>
          {label} {required && <span className={'text-red'}>*</span>}
        </h4>
      )}
      <div className={'relative'}>
        <input
          type={type}
          placeholder={placeholder}
          value={value}
          className={`h-14 w-full rounded-lg bg-natreul/30 px-6 text-dark placeholder:text-dark/50 md:h-12 md:text-16 ${inputClassName}`}
          onChange={onChange}
        />
        {prefix && <span className={'absolute right-6 top-4'}>{prefix}</span>}
      </div>
      {error && <TextError text={error} />}
    </div>
  );
};

export default Input;
