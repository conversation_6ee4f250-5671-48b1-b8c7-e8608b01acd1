import Link from 'next/link';

import BlogThumbnail from '@/components/BlogThumbnail';

const NineBlog = () => {
  return (
    <div
      className={`items-normal mx-auto mb-[168px] flex h-full w-full max-w-[1728px] px-24`}
    >
      <div
        className={`relative flex w-full flex-col items-start justify-between px-24`}
      >
        <div className={`grid grid-cols-3 gap-10`}>
          {/* Blog Thumbnail Item Section */}
          <BlogThumbnail />
          {/* Blog Thumbnail Item Section */}
          <BlogThumbnail />
          {/* Blog Thumbnail Item Section */}
          <BlogThumbnail />
          {/* Blog Thumbnail Item Section */}
          <BlogThumbnail />
          {/* Blog Thumbnail Item Section */}
          <BlogThumbnail />
          {/* Blog Thumbnail Item Section */}
          <BlogThumbnail />
          {/* Blog Thumbnail Item Section */}
          <BlogThumbnail />
          {/* Blog Thumbnail Item Section */}
          <BlogThumbnail />
          {/* Blog Thumbnail Item Section */}
          <BlogThumbnail />
        </div>
        <Link
          href="/"
          className={`mx-auto mt-[56px] rounded-full bg-[#21409A] px-8 py-4 text-[18px] font-bold text-[#FDFDFD]`}
        >
          Read more
        </Link>
      </div>
    </div>
  );
};

export default NineBlog;
