import SVG from 'react-inlinesvg';

interface IProps {
  name?: string;
  uri?: string;
  className?: string;
  color?: string;
  width?: number;
  height?: number;
  size?: any
}

const Icon = ({
  name,
  uri,
  className,
  color,
  height,
  width,
  size = 24,
  ...props
}: IProps) => {
  return (
    <SVG
      color={color}
      className={`inline ${className}`}
      height={height || size}
      width={width || size}
      src={uri || `/assets/icons/${name}.svg`}
      {...props}
    />
  );
};

export default Icon;
