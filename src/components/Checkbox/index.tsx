import React from 'react';

import Icon from '@/components/Icon';

interface IProps {
  title?: string;
  label?: string;
  value?: boolean;
  onChange: (value: boolean) => void;
}
const Checkbox = ({ title, label, value, onChange }: IProps) => {
  return (
    <div>
      {title && <h4 className={'mb-2 text-blue'}>{title}</h4>}

      <button
        onClick={() => onChange(!value)}
        className={
          'min-h-14 flex w-full items-center justify-center gap-x-4 rounded-lg bg-natreul/30 px-6 py-4'
        }
      >
        <div
          className={`flex h-6 w-6 items-center justify-center rounded-lg ${
            value ? 'bg-blue' : 'bg-blue/30'
          }`}
        >
          {value && <Icon name="tick-square" className={'fill-white'} />}
        </div>
        <p className={'flex-1 text-left'}>{label}</p>
      </button>
    </div>
  );
};

export default Checkbox;
