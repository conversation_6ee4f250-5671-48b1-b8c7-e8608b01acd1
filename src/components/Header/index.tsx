import classNames from 'classnames';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useContext, useEffect, useState } from 'react';
import { Popover } from 'react-tiny-popover';

import Button from '@/components/Button';
import Icon from '@/components/Icon';
import { AppContext } from '@/context/context';
import useTrans from '@/hooks/useTrans';

import { COMPANY, PLATFORMS } from '../Footer';

interface IProps {
  className?: string;
}
const Header = ({ className = '' }: IProps) => {
  const trans = useTrans();
  const router = useRouter();
  const { locale } = useRouter();
  const [openPlatformPopover, setOpenPlatformPopover] = useState(false);
  const [openCompanyPopover, setOpenCompanyPopover] = useState(false);
  const [headerSecondary, setHeaderSecondary] = useState(false);
  const [showMenuMobile, setShowMenuMobile] = useState(false);
  const { setHeaderItem } = useContext<any>(AppContext);
  const [isDreamKids, setIsDreamKids] = useState(false);

  useEffect(() => {
    setIsDreamKids(router?.pathname?.includes('dreamkids'));
  }, [router?.pathname]);

  const handleScroll = () => {
    const currentScrollPos = window.scrollY;

    setHeaderSecondary(currentScrollPos >= 300);
  };

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);

    return () => window.removeEventListener('scroll', handleScroll);
  });

  useEffect(() => {
    // @ts-ignore
    document.getElementsByTagName('html')[0].style.overflow = showMenuMobile
      ? 'hidden'
      : 'auto';
    // @ts-ignore
    document.getElementsByTagName('body')[0].style.overflow = showMenuMobile
      ? 'hidden'
      : 'auto';
  }, [showMenuMobile]);

  return (
    <header
      className={classNames(
        `fixed left-0 top-0 z-20 w-full duration-300 transition-[background] ease-out ${className}`,
        {
          'bg-header': headerSecondary && !showMenuMobile,
          'bg-blue h-[100dvh]': showMenuMobile,
          'h-[120px]': !showMenuMobile,
        },
      )}
    >
      <nav
        className={classNames(
          `mx-auto flex h-full max-w-[1654px] px-4 w-full`,
          {
            'text-black items-center justify-between h-full': !showMenuMobile,
            'text-white flex-col h-[100dvh] px-0': showMenuMobile,
          },
        )}
      >
        <div
          className={`flex w-full items-center lg:justify-between min-lg:space-x-24 ${showMenuMobile ? 'h-[120px] px-4' : ''
            }`}
        >
          {
            isDreamKids
              ? (
                <Image width={240} height={30} src='/assets/images/dreamkids-logo.png' alt="DreamKids Logo" />
              )
              : (
                <Link href={'/'} onClick={() => setShowMenuMobile(false)}>
                  <Icon
                    name={showMenuMobile ? 'logo-white' : 'logo'}
                    width={230}
                    height={64}
                  />
                </Link>
              )
          }
          {
            !isDreamKids &&
            <button
              onClick={() => setShowMenuMobile(!showMenuMobile)}
              className={'hidden lg:block'}
            >
              <Icon
                name={showMenuMobile ? 'close' : 'menu'}
                className={showMenuMobile ? 'fill-white' : 'fill-blue'}
              />
            </button>
          }
          {!showMenuMobile && (
            <div className={`flex flex-row gap-x-16 lg:hidden`}>
              <Link
                href={'/'}
                onClick={() => setHeaderItem('introduce')}
                className={'text-blue'}
              >
                <b>{trans.introduce}</b>
              </Link>
              <Link
                href={'/'}
                onClick={() => setHeaderItem('features')}
                className={'text-blue'}
              >
                <b>{trans.features}</b>
              </Link>
              <Popover
                onClickOutside={() => setOpenPlatformPopover(false)}
                isOpen={openPlatformPopover}
                positions={['bottom', 'right']}
                align={'start'}
                content={
                  <ul
                    className={`space-y-6 rounded-2xl bg-white px-4 py-8 shadow-normal`}
                  >
                    {PLATFORMS.map((link) => (
                      <li
                        key={link.url}
                        className={
                          link.url === router.pathname
                            ? 'font-bold text-blue'
                            : ''
                        }
                      >
                        <Link href={link.url} className={'whitespace-nowrap'}>
                          {/* @ts-ignore */}
                          {trans[link.text]}
                        </Link>
                      </li>
                    ))}
                  </ul>
                }
              >
                <button
                  className={'flex items-center space-x-2'}
                  onClick={() => setOpenPlatformPopover(!openPlatformPopover)}
                >
                  <b className={'text-blue'}>{trans.platforms}</b>
                  <Icon name={'arrow-down'} className={`fill-black`} />
                </button>
              </Popover>
              <Link
                className={`text-blue ${router.pathname === 'pricing' ? 'underline' : ''
                  }`}
                href={'/pricing'}
                onClick={() => setHeaderItem('')}
              >
                <b>{trans.pricing}</b>
              </Link>
              <Popover
                onClickOutside={() => setOpenCompanyPopover(false)}
                isOpen={openCompanyPopover}
                positions={['bottom', 'right']}
                align={'start'}
                content={
                  <ul
                    className={`space-y-6 rounded-2xl bg-white px-4 py-8 shadow-normal`}
                  >
                    {COMPANY.map((link) => (
                      <li
                        key={link.url}
                        className={
                          link.url === router.pathname
                            ? 'font-bold text-blue'
                            : ''
                        }
                      >
                        <Link href={link.url} className={'whitespace-nowrap'}>
                          {/* @ts-ignore */}
                          {trans[link.text]}
                        </Link>
                      </li>
                    ))}
                  </ul>
                }
              >
                <button
                  className={'flex items-center space-x-2'}
                  onClick={() => setOpenCompanyPopover(!openCompanyPopover)}
                >
                  <b className={'text-blue'}>{trans.resources}</b>
                  <Icon name={'arrow-down'} className={`fill-black`} />
                </button>
              </Popover>
            </div>
          )}
        </div>
        <div className={`flex items-center gap-x-8 lg:hidden`}>
          <Link
            href={'https://school.mamchoila.com.vn/'}
            className={'whitespace-nowrap'}
          >
            {trans.demoLink}
          </Link>
          <Button url={'/get-quote'}>{trans.requestADemo}</Button>
          <ul
            className={classNames(
              `relative flex items-center space-x-4 before:absolute before:left-[28px] before:top-[4px] before:h-[18px] before:w-[1px] before:bg-secondary`,
              {
                'before:bg-white': showMenuMobile,
              },
            )}
          >
            {['en', 'vi'].map((lang) => (
              <li key={lang}>
                <Link
                  href={router.asPath}
                  locale={lang}
                  onClick={() => localStorage.setItem('language', lang)}
                  className={`${locale === lang ? 'opacity-100' : 'opacity-30'
                    }`}
                >
                  <b className={'text-16'}>{lang.toUpperCase()}</b>
                </Link>
              </li>
            ))}
          </ul>
        </div>
        {/* Mobile */}
        {showMenuMobile && (
          <div
            className={
              'hidden h-0 flex-1 space-y-12 overflow-auto px-4 pb-6 text-18 lg:block'
            }
          >
            <Link
              className={'block'}
              href={'/'}
              onClick={() => setHeaderItem('introduce')}
            >
              <b>{trans.introduce}</b>
            </Link>
            <Link
              className={'block'}
              href={'/'}
              onClick={() => setHeaderItem('features')}
            >
              <b>{trans.features}</b>
            </Link>
            <Popover
              onClickOutside={() => setOpenPlatformPopover(false)}
              isOpen={openPlatformPopover}
              positions={['bottom', 'right']}
              align={'start'}
              content={
                <ul
                  className={`w-[320px] space-y-6 rounded-2xl bg-white p-4 shadow-normal`}
                >
                  {PLATFORMS.map((link) => (
                    <li
                      key={link.url}
                      onClick={() => setShowMenuMobile(false)}
                      className={
                        link.url === router.pathname
                          ? 'font-bold text-blue'
                          : ''
                      }
                    >
                      <Link href={link.url}>
                        {/* @ts-ignore */}
                        {trans[link.text]}
                      </Link>
                    </li>
                  ))}
                </ul>
              }
            >
              <button
                className={'flex items-center space-x-2'}
                onClick={() => setOpenPlatformPopover(!openPlatformPopover)}
              >
                <b className={'text-white'}>{trans.platforms}</b>
                <Icon name={'arrow-down'} className={`fill-white`} />
              </button>
            </Popover>
            <Link
              className={'block text-white'}
              href={'/pricing'}
              onClick={() => setShowMenuMobile(false)}
            >
              <b>{trans.pricing}</b>
            </Link>
            <Popover
              onClickOutside={() => setOpenCompanyPopover(false)}
              isOpen={openCompanyPopover}
              positions={['bottom', 'right']}
              align={'start'}
              content={
                <ul
                  className={`w-[320px] space-y-6 rounded-2xl bg-white px-4 py-8 shadow-normal`}
                >
                  {COMPANY.map((link) => (
                    <li
                      key={link.url}
                      onClick={() => setShowMenuMobile(false)}
                      className={
                        link.url === router.pathname
                          ? 'font-bold text-blue'
                          : ''
                      }
                    >
                      <Link href={link.url}>
                        {/* @ts-ignore */}
                        {trans[link.text]}
                      </Link>
                    </li>
                  ))}
                </ul>
              }
            >
              <button
                className={'flex items-center space-x-2'}
                onClick={() => setOpenCompanyPopover(!openCompanyPopover)}
              >
                <b className={'text-white'}>{trans.resources}</b>
                <Icon name={'arrow-down'} className={`fill-white`} />
              </button>
            </Popover>
            <Link href={'https://school.mamchoila.com.vn/'} className={'block'}>
              <b>{trans.demoLink}</b>
            </Link>
            <Button url={'/get-quote'} className={'bg-white !text-blue'}>
              {trans.requestADemo}
            </Button>
            <ul
              className={classNames(
                `relative flex items-center space-x-4 before:absolute before:left-[28px] before:top-[4px] before:h-[18px] before:w-[1px] before:bg-secondary`,
                {
                  'before:bg-white': showMenuMobile,
                },
              )}
            >
              {['en', 'vi'].map((lang) => (
                <li key={lang}>
                  <Link
                    href={router.asPath}
                    locale={lang}
                    onClick={() => localStorage.setItem('language', lang)}
                    className={`${locale === lang ? 'opacity-100' : 'opacity-30'
                      }`}
                  >
                    <b className={'text-16'}>{lang.toUpperCase()}</b>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        )}
      </nav>
    </header>
  );
};

export default Header;
