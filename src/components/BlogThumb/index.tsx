import Image from 'next/image';
import Link from 'next/link';

const BlogThumb = () => {
  return (
    <div className={`w-full rounded-[8px] bg-[#FDFDFD] shadow-normal`}>
      <Image
        src="/assets/images/help-banner.jpg"
        alt="Help Banner Images"
        width={500}
        height={500}
        className={`mb-8 h-[320px] w-full rounded-t-[8px] object-cover`}
      />
      <div className={`flex flex-col items-baseline px-8 pb-12 text-left`}>
        <p className={`mb-4 text-[#21409A] underline`}>Education</p>
        <p className={`mb-4 text-[24px] font-bold text-[#58595B]`}>
          Streamlining Success: How to use Landing Pages to fill Enrollment
          Spots
        </p>
        <p className={`text-[14px] font-bold text-[#58595B] opacity-50`}>
          June 22, 2023 14:25
        </p>
        <Link href="/" className={`mt-4 underline`}>
          Read
        </Link>
      </div>
    </div>
  );
};

export default BlogThumb;
