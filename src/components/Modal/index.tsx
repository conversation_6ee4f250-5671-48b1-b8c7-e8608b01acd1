import React, { useEffect, useRef } from 'react';

interface IModalProps {
  visibleModal: boolean;
  wrapperClassName?: string;
  contentClassName?: string;
  bodyClassName?: string;
  children?: React.ReactElement | undefined;
  onClose: () => void;
  onConfirm?: () => void;
}

const Modal = ({
  visibleModal,
  wrapperClassName = '',
  contentClassName = '',
  bodyClassName = '',
  children,
  onClose,
}: IModalProps) => {
  const modalRef = useRef<any>(null);

  useEffect(() => {
    if (visibleModal) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
  }, [visibleModal]);

  return (
    <>
      <div
        className={`fixed inset-0 z-[101] m-auto flex h-fit max-w-[1084px] items-center justify-center overflow-hidden px-6 outline-none transition-all duration-300 focus:outline-none ${wrapperClassName} ${
          visibleModal ? 'visible opacity-[1]' : 'invisible opacity-0'
        }`}
      >
        <div
          ref={modalRef}
          className={`relative max-h-[90vh] min-h-[460px] w-fit overflow-y-auto overflow-x-hidden rounded-lg bg-white px-[106px] py-[48px] shadow-lg ${contentClassName}`}
        >
          <div className={`relative ${bodyClassName}`}>{children}</div>
        </div>
      </div>
      <button
        onClick={onClose}
        className={`bg-grey700 fixed inset-0 z-[100] transition-all duration-300 ${
          visibleModal ? 'visible opacity-[1]' : 'invisible opacity-0'
        }`}
      />
    </>
  );
};

export default Modal;
