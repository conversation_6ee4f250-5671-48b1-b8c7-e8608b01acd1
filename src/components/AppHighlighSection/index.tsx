import { useRouter } from 'next/router';
import React from 'react';

import AppImage from '@/components/AppImage';
import Title from '@/components/Title';
import { sectionClassName } from '@/constants';
import type { ILanguageInput } from '@/typings/common';

interface IProps {
  title?: string;
  data?: {
    title: ILanguageInput;
    description: ILanguageInput;
    image: string;
  }[];
}
const AppHighlightSection = ({ title, data }: IProps) => {
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <section className={sectionClassName}>
      <Title text={title || ''} />
      <div
        className={`mt-10 grid grid-cols-3 gap-x-12 lg:grid-cols-1 lg:gap-y-6`}
      >
        {data?.map((item, index) => (
          <div key={index}>
            <h4 className={`barlow text-24 font-bold md:text-18`}>
              {item.title[locale]}
            </h4>
            <p className={'barlow mb-12 mt-4 md:mb-4 md:mt-2'}>
              {item.description[locale]}
            </p>
            <div className={'aspect-[402/280]'}>
              <AppImage
                src={item.image}
                className={'rounded-2xl md:rounded-lg'}
              />
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default AppHighlightSection;
