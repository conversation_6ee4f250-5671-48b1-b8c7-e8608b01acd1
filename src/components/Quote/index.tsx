import { useRouter } from 'next/router';

import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import type { ILanguageInput } from '@/typings/common';

import AppImage from '../AppImage';
import AppLink from '../AppLink';
import Button from '../Button';

const DATA = {
  subTitle: {
    en: 'Let’s book your free tour',
    vi: 'Hãy cùng trải nghiệm thử hệ thống Mầm Chồi Lá',
  },
  title: {
    en: "We'll show you exactly what a unified childcare experience looks like.",
    vi: 'Mầm Chồi Lá sẽ cho bạn trải nghiệm một hệ thống quản lý giáo dục mầm non toàn diện.',
  },
  description: {
    en: 'Wow-you’ve read a lot just now. Why not let our specialists show you everything about our childcare app in just 15 minutes online? Learn about all the tools you need to grow your center!',
    vi: 'Wow - bạn vừa đọc nhiều thông tin rồi đó. Tại sao bạn không để chuyên gia của Mầm Chồi <PERSON> hướng dẫn bạn mọi thứ về hệ thống quản lý trường mầm non trong vòng 15 phút trực tuyến? Tìm hiểu về tất cả các công cụ mà bạn cần để phát triển trung tâm của mình!',
  },
};
const Quote = () => {
  const locale = useRouter().locale as keyof ILanguageInput;
  const trans = useTrans();

  return (
    <section className={`${sectionClassName} max-w-[1600px]`}>
      <div
        className={`grid grid-cols-[auto_min(506px,40%)] gap-x-20 rounded-[16px] bg-primaryGradient lg:grid-cols-[auto_min(506px,33%)] lg:gap-x-6 md:grid-cols-1`}
      >
        <div className={`py-21 pl-32 lg:p-6`}>
          <div className={'mb-10 space-y-8 text-light md:space-y-4'}>
            <p className={`barlow text-18 underline`}>
              <b>{DATA.subTitle[locale]}</b>
            </p>
            <h3 className={`text-32 md:text-24`}>{DATA.title[locale]}</h3>
            <p>{DATA.description[locale]}</p>
          </div>
          <div
            className={`flex flex-row items-center gap-x-16 lg:gap-8 md:flex-col md:items-start`}
          >
            <Button
              url={'/get-quote'}
              className={'bg-secondaryGradient !text-blue'}
            >
              {trans.requestADemo}
            </Button>
            <AppLink type={'light'} href={'/'} text={trans.getAQuote} />
          </div>
        </div>
        <div className={'md:hidden'}>
          <AppImage
            src={'/assets/images/quote-section.png'}
            className={'!object-contain'}
          />
        </div>
      </div>
    </section>
  );
};

export default Quote;
