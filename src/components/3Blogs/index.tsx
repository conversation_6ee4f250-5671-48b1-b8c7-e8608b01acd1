import Link from 'next/link';

import BlogThumb from '@/components/BlogThumb';

const ThreeBlog = () => {
  return (
    <div
      className={`items-normal mx-auto mb-[168px] flex h-full w-full max-w-[1728px] px-24`}
    >
      <div
        className={`relative flex w-full flex-col items-start justify-between px-24`}
      >
        <p
          className={`text-sm mb-16 w-full text-center font-bold text-[#EF4060] underline`}
        >
          More resources to help you
        </p>
        <div className={`grid grid-cols-3 gap-8`}>
          {/* Blog Thumbnail Item Section */}
          <BlogThumb />
          {/* Blog Thumbnail Item Section */}
          <BlogThumb />
          {/* Blog Thumbnail Item Section */}
          <BlogThumb />
        </div>
        <Link
          href="/"
          className={`mx-auto mt-[56px] rounded-full bg-[#21409A] px-8 py-4 text-[18px] font-bold text-[#FDFDFD]`}
        >
          Read more
        </Link>
      </div>
    </div>
  );
};

export default ThreeBlog;
