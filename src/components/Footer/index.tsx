import Link from 'next/link';
import { useRouter } from 'next/router';
import { useContext, useEffect, useState } from 'react';

import AppLink from '@/components/AppLink';
import Icon from '@/components/Icon';
import { AppContext } from '@/context/context';
import useTrans from '@/hooks/useTrans';
import type { ILanguageInput } from '@/typings/common';

export const SOLUTIONS = [
  {
    text: 'introduce',
    url: '/',
  },
  {
    text: 'pricing',
    url: '/pricing',
  },
];

export const PLATFORMS = [
  {
    text: 'parentsApplication',
    url: '/parent-app-download',
  },
  // {
  //   text: 'educationManagementApplicationForEducators',
  //   url: '/educators-application',
  // },
  // {
  //   text: 'drivingApplicationForSchoolBusDrivers',
  //   url: '/bus-driving-application',
  // },
  // {
  //   text: 'operationSystemForPrincipals',
  //   url: '/principals-management',
  // },
];

export const FEATURES = [
  {
    text: {
      vi: 'Website trường học',
      en: '',
    },
    url: '/#school-website',
  },
  {
    text: {
      vi: 'Tuyển sinh trực tuyến',
      en: '',
    },
    url: '/',
  },
  {
    text: {
      vi: 'Quản lý Học sinh',
      en: '',
    },
    url: '/',
  },
  {
    text: {
      vi: 'Cấu hình mẫu Học phí',
      en: '',
    },
    url: '/',
  },
  {
    text: {
      vi: 'Quản lý Học phí tự động',
      en: '',
    },
    url: '/',
  },
  {
    text: {
      vi: 'Điểm danh tự động',
      en: '',
    },
    url: '/',
  },
  {
    text: {
      vi: 'Quản lý Nhân viên, Giáo viên',
      en: '',
    },
    url: '/',
  },
  {
    text: {
      vi: 'Cấu hình mẫu Lương',
      en: '',
    },
    url: '/',
  },
  {
    text: {
      vi: 'Quản lý Lương tự động',
      en: '',
    },
    url: '/',
  },
  {
    text: {
      vi: 'Tin nhắn Zalo thông báo tự động',
      en: '',
    },
    url: '/',
  },
  {
    text: {
      vi: 'Cổng thông tin trực tuyến cho Phụ huynh',
      en: '',
    },
    url: '/',
  },
  {
    text: {
      vi: 'Báo cáo Tài chính tự động',
      en: '',
    },
    url: '/',
  },
];

export const COMPANY = [
  {
    text: 'aboutUs',
    url: '/about-us',
  },
  {
    text: 'leadership',
    url: '/leadership',
  },
  // {
  //   text: 'pressReleases',
  //   url: '/press-release',
  // },
  {
    text: 'newsroom',
    url: '/newsroom',
  },
  // {
  //   text: 'customerStories',
  //   url: '/customer-stories',
  // },
  {
    text: 'helpCenter',
    url: '/help-center',
  },
  {
    text: 'internetSafety',
    url: '/internet-safety',
  },
];

export const CONTACT_SALES = [
  {
    text: 'southAreaSales',
    phone: '+84 0334542911',
  },
  {
    text: 'customerSupportLine',
    phone: '+84 0334542911',
  },
];

const POLICIES = [
  {
    text: 'home',
    url: '/',
  },
  {
    text: 'childSafetyProtection',
    url: '/child-safety',
  },
  {
    text: 'termsOfUse',
    url: '/terms',
  },
  {
    text: 'privacyPolicies',
    url: '/privacy',
  },
];

const SOCIALS = [
  {
    icon: 'facebook',
    url: 'https://www.facebook.com/mamchoilaonline',
  },
  {
    icon: 'instagram',
    url: 'https://www.instagram.com/mamchoilavn/',
  },
  {
    icon: 'x',
    url: 'https://twitter.com/Mamchoila',
  },
  {
    icon: 'youtube',
    url: 'https://www.youtube.com/@mamchoilaonline',
  },
  {
    icon: 'tiktok',
    url: 'https://www.tiktok.com/@mamchoilaonline',
  },
  {
    icon: 'pinterest',
    url: 'https://www.pinterest.com/mamchoila/',
  },
];

interface IProps {
  secondary?: boolean;
  className?: string;
}
const Footer = ({ secondary = false, className = '' }: IProps) => {
  const trans = useTrans();
  const router = useRouter();
  const locale = useRouter().locale as keyof ILanguageInput;
  const { setHeaderItem } = useContext<any>(AppContext);
  const [isDreamKids, setIsDreamKids] = useState(false);

  useEffect(() => {
    setIsDreamKids(router?.pathname?.includes('dreamkids'));
  }, [router?.pathname]);

  if (isDreamKids) {
    return null;
  }

  return (
    <footer className={`duration-300 ease-out ${className}`}>
      <nav
        className={
          'mx-auto mb-16 grid max-w-[1600px] grid-cols-[342px_auto] justify-between gap-x-14 border-t-1 border-natreul px-4 pt-16 lg:grid-cols-1 lg:pt-8'
        }
      >
        <div className={'space-y-6 md:space-y-4 md:text-16'}>
          <Icon
            name={'logo'}
            className={`${secondary ? 'fill-white' : 'fill-primary'}`}
            width={230}
            height={64}
          />
          <p>Mầm Chồi Lá Education Technology Co.LTD</p>
          <p>
            <b>Mã số thuế:</b> 04678901475
          </p>
          <p>
            <b>{trans.address}:</b> 26 Đường 4, KP.4, Phường Linh Chiểu, Thành
            phố Thủ Đức, TP.HCM
          </p>
          <p>
            <b>{trans.hotline}:</b> +84 0334542911
          </p>
        </div>
        <div
          className={
            'flex justify-between gap-x-16 lg:mt-8 lg:flex-col lg:gap-y-8 md:text-16'
          }
        >
          <div className={'max-w-[320px] space-y-8'}>
            <p className={`text-18 font-bold`}>{trans.solution}</p>
            <ul className={`space-y-6`}>
              {SOLUTIONS.map((link) => (
                <li key={link.url}>
                  {/* @ts-ignore */}
                  <Link href={link.url}>{trans[link.text]}</Link>
                </li>
              ))}
            </ul>
          </div>

          <div className={'max-w-[320px] space-y-8'}>
            <p className={`text-18 font-bold`}>{trans.features}</p>
            <ul className={`space-y-6`}>
              {FEATURES.map((link, index) => (
                <li
                  key={index}
                  onClick={() => setHeaderItem(`feature-${index + 1}`)}
                >
                  {/* @ts-ignore */}
                  <Link href={link.url}>{link.text[locale]}</Link>
                </li>
              ))}
            </ul>
          </div>

          {/* <div className={'max-w-[320px] space-y-8'}> */}
          {/*  <p className={`text-18 font-bold`}>{trans.platforms}</p> */}
          {/*  <ul className={`space-y-6`}> */}
          {/*    {PLATFORMS.map((link) => ( */}
          {/*      <li key={link.url}> */}
          {/*        /!* @ts-ignore *!/ */}
          {/*        <Link href={link.url}>{trans[link.text]}</Link> */}
          {/*      </li> */}
          {/*    ))} */}
          {/*  </ul> */}
          {/* </div> */}

          <div className={'space-y-8'}>
            <p className={`text-18 font-bold`}>{trans.company}</p>
            <ul className={`space-y-6`}>
              {COMPANY.map((link) => (
                <li key={link.url}>
                  {/* @ts-ignore */}
                  <Link href={link.url}>{trans[link.text]}</Link>
                </li>
              ))}
            </ul>
          </div>

          <div className={'space-y-8'}>
            <p className={`text-18 font-bold`}>{trans.contactSales}</p>
            <ul className={`space-y-6`}>
              {CONTACT_SALES.map((link, index) => (
                <li key={index}>
                  {/* @ts-ignore */}
                  <p>{trans[link.text]}</p>
                  <Link className={'text-blue'} href={`tel:${link.phone}`}>
                    {link.phone}
                  </Link>
                </li>
              ))}
              <li>
                <AppLink
                  href={`mailto:<EMAIL>`}
                  text={'<EMAIL>'}
                />
              </li>
            </ul>
          </div>
        </div>
      </nav>
      <nav className={`bg-blue text-[#FFFFFF]`}>
        <div
          className={`mx-auto flex h-full max-w-[1600px] items-center justify-between px-4 py-8 lg:flex-col lg:items-start lg:gap-y-8`}
        >
          <p className={`text-12 md:order-last md:text-16`}>
            © 2023 Mam Choi La - All rights served.
          </p>
          <ul
            className={`flex items-center gap-x-4 text-14 md:flex-col md:items-start md:gap-y-6 md:text-16`}
          >
            {POLICIES.map((item) => (
              <Link href={item.url} key={item.url}>
                {/* @ts-ignore */}
                {trans[item.text]}
              </Link>
            ))}
          </ul>
          <ul className={`flex items-center gap-x-4`}>
            {SOCIALS.map((item) => (
              <Link href={item.url} key={item.icon} target={'_blank'}>
                <Icon name={item.icon} className={'fill-white'} />
              </Link>
            ))}
          </ul>
        </div>
      </nav>
    </footer>
  );
};

export default Footer;
