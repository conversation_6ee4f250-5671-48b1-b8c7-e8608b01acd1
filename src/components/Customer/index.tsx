import { useRouter } from 'next/router';

import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import type { ILanguageInput } from '@/typings/common';

import AppImage from '../AppImage';
import AppLink from '../AppLink';
import Button from '../Button';

const CONTACTS = [
  {
    name: 'Messenger',
    url: '/',
  },
  {
    name: 'Instagram',
    url: '/',
  },
  {
    name: '<PERSON><PERSON>',
    url: '/',
  },
  {
    name: 'Telegram',
    url: '/',
  },
  {
    name: 'Twitter',
    url: '/',
  },
  {
    name: 'Whatsapp',
    url: '/',
  },
  {
    name: 'Viber',
    url: '/',
  },
];

const DATA = {
  title: {
    en: 'Get a world-class customer support & free resources',
    vi: 'Hỗ trợ khách hàng & nguồn tài nguyên thông tin miễn phí',
  },
  description: {
    en: 'Our world-class customer support team is here to support your center. And that’s not all — join free weekly podcasts, webinars, and masterclasses dedicated to supporting you and enriching our childhood educator community!',
    vi: 'Đội ngũ hỗ trợ khách hàng chuyên nghiệp của Mầm Chồi Lá luôn sẵn sàng hỗ trợ trung tâm của bạn. Cùng tham gia vào các buổi podcast hàng tuần, webinar và các khóa học chuyên sâu miễn phí, nhằm hỗ trợ bạn và làm phong phú cộng đồng giáo viên mầm non của chúng ta!',
  },
};

const CustomerSupport = () => {
  const locale = useRouter().locale as keyof ILanguageInput;
  const trans = useTrans();

  return (
    <section
      className={`grid grid-cols-[auto_500px] gap-x-[116px] ${sectionClassName} lg:grid-cols-1 lg:gap-y-8`}
    >
      <div>
        <p className={`barlow text-18 text-red underline`}>
          <b>{DATA.title[locale]}</b>
        </p>
        <p className={`mb-10 mt-8 text-24 md:mb-8 md:mt-4 md:text-16`}>
          {DATA.description[locale]}
        </p>
        <div
          className={`flex items-center gap-x-16 md:flex-col md:items-start md:gap-y-8`}
        >
          <Button outline url={`tel: 84 0334542911`}>
            {trans.hotline}: +84 0334542911
          </Button>
          <AppLink
            href={'mailto:<EMAIL>'}
            text={'<EMAIL>'}
            target={'_blank'}
          />
        </div>
      </div>
      <div className={`flex items-center gap-x-[72px]`}>
        <AppImage
          src={'/assets/images/customer-support.png'}
          className={'!w-[282px] lg:hidden'}
        />
        <ul className={'space-y-6'}>
          {CONTACTS.map((item) => (
            <li key={item.name}>
              <AppLink href={item.url} text={item.name} target={'_blank'} />
            </li>
          ))}
        </ul>
      </div>
    </section>
  );
};

export default CustomerSupport;
