import classNames from 'classnames';
// eslint-disable-next-line import/no-extraneous-dependencies
import en from 'date-fns/locale/en-US';
// eslint-disable-next-line import/no-extraneous-dependencies
import vi from 'date-fns/locale/vi';
// eslint-disable-next-line import/no-extraneous-dependencies
import { range } from 'lodash';
import * as React from 'react';
import ReactDatePicker, { registerLocale } from 'react-datepicker';

import Icon from '@/components/Icon';
import TextError from '@/components/TextError';

registerLocale('vi', vi);
registerLocale('en', en);

interface IDatePickerProps {
  label?: string;
  value: any;
  minDate?: Date | null | undefined;
  maxDate?: Date | null | undefined;
  placeholderText?: string;
  disabled?: boolean;
  showDefaultStyle?: boolean;
  className?: string;
  wrapperClassName?: string;
  showIcon?: boolean;
  onChange: (date: Date | null) => void;
  error?: string;
}

const Datepicker = ({
  label,
  value,
  minDate = null,
  maxDate = null,
  placeholderText = 'DD/MM/YYYY',
  disabled = false,
  onChange,
  showDefaultStyle,
  wrapperClassName = '',
  showIcon = true,
  className = '',
  error,
}: IDatePickerProps) => {
  const years = range(1923, 2043);
  const months = [
    'january',
    'february',
    'march',
    'april',
    'may',
    'june',
    'july',
    'august',
    'september',
    'october',
    'november',
    'december',
  ];
  return (
    <div>
      {label && (
        <h4 className={'mb-2 text-blue'}>
          {label} <span className={'text-red'}>*</span>
        </h4>
      )}
      <div className={`flex-1 space-y-2 rounded-lg bg-natreul/30 ${className}`}>
        <div
          className={`relative pr-4 ${wrapperClassName} ${
            disabled ? 'cursor-not-allowed' : ''
          }`}
        >
          {/* @ts-ignore */}
          <ReactDatePicker
            locale={`vi`}
            disabled={disabled}
            dateFormat="dd/MM/yyyy"
            placeholderText={placeholderText}
            selected={Number(value) ? value : null}
            minDate={minDate}
            maxDate={maxDate}
            onChange={(date) => onChange(date)}
            className={
              showDefaultStyle
                ? ''
                : classNames('text-14 cursor-pointer bg-transparent w-full', {
                    'text-dark': !!Number(value),
                    'placeholder:text-dark/70': !Number(value),
                    'cursor-not-allowed': disabled,
                  })
            }
            renderCustomHeader={({
              date,
              changeYear,
              changeMonth,
              decreaseMonth,
              increaseMonth,
              prevMonthButtonDisabled,
              nextMonthButtonDisabled,
            }) => (
              <div className={'flex items-center justify-between'}>
                <button
                  onClick={decreaseMonth}
                  disabled={prevMonthButtonDisabled}
                  className={`rdrNextPrevButton rdrPprevButton ${
                    prevMonthButtonDisabled
                      ? 'cursor-not-allowed opacity-50 hover:bg-transparent'
                      : ''
                  }`}
                >
                  <Icon
                    name={'triangle-right'}
                    className={'rotate-180 fill-dark'}
                  />
                </button>
                <div className="rdrMonthAndYearPickers">
                  <div className={'rdrMonthPicker'}>
                    <select
                      value={date.getFullYear()}
                      // eslint-disable-next-line @typescript-eslint/no-shadow
                      onChange={({ target: { value } }) =>
                        changeYear(Number(value))
                      }
                    >
                      {years.map((option) => (
                        <option key={option} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className={'rdrYearPicker'}>
                    <select
                      value={months[date.getMonth()]}
                      // eslint-disable-next-line @typescript-eslint/no-shadow
                      onChange={({ target: { value } }) =>
                        changeMonth(months.indexOf(value))
                      }
                    >
                      {months.map((option) => (
                        <option key={option} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                <button
                  onClick={increaseMonth}
                  disabled={nextMonthButtonDisabled}
                  className={'rdrNextPrevButton rdrNextButton'}
                >
                  <Icon name={'triangle-right'} className={'fill-dark'} />
                </button>
              </div>
            )}
          />
          {showIcon && (
            <Icon
              name="calendar"
              className={`absolute right-[12px] top-3 fill-primary`}
            />
          )}
        </div>
      </div>
      {error && <TextError text={error} />}
    </div>
  );
};

export default Datepicker;
