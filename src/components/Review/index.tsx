import Image from 'next/image';

import Icon from '@/components/Icon';

const CustomerReview = () => {
  return (
    <div
      className={`items-normal mx-auto mb-[88px] flex h-full w-full max-w-[1728px] px-24`}
    >
      <div
        className={`relative flex w-full flex-row items-start justify-between px-24 text-center`}
      >
        {/* Review Item Section */}
        <div
          className={`relative flex h-[420px] w-[18vw] flex-col items-center gap-y-6 rounded-[16px] bg-[#FDFDFD] px-8 py-10`}
        >
          <Icon
            color={'white'}
            name={'airdrop'}
            className={`
              absolute
              left-[48px]
              top-[48px]
              opacity-[.03]
              [&>*]:fill-[#21409A]
            `}
            width={194}
            height={194}
          />
          <p className={`text-[20px] font-light text-[#58595B]`}>
            “Love being able to see how my child’s day is going with quick and
            easy updates.”
          </p>
          <p
            className={`x-auto absolute bottom-[96px] text-[16px] font-bold text-[#58595B]`}
          >
            Emma,
            <br />
            <span className={`text-[14px] font-normal`}>
              Mom of 3 years old boy
            </span>
          </p>
          <Image
            src="/assets/images/star-rating.png"
            alt="Star Rating"
            width={500}
            height={500}
            className={`x-auto absolute bottom-[48px] h-[24px] w-auto object-cover`}
          />
        </div>
        {/* Review Item Section */}
        <div
          className={`relative flex h-[420px] w-[18vw] flex-col items-center gap-y-6 rounded-[16px] bg-[#FDFDFD] px-8 py-10`}
        >
          <Icon
            color={'white'}
            name={'airdrop'}
            className={`
              absolute
              left-[48px]
              top-[48px]
              opacity-[.03]
              [&>*]:fill-[#21409A]
            `}
            width={194}
            height={194}
          />
          <p className={`text-[20px] font-light text-[#58595B]`}>
            “So easy to use! I love that I can interact with my child’s
            teacher.”
          </p>
          <p
            className={`x-auto absolute bottom-[96px] text-[16px] font-bold text-[#58595B]`}
          >
            David,
            <br />
            <span className={`text-[14px] font-normal`}>
              Dad of 2 years old twin girls
            </span>
          </p>
          <Image
            src="/assets/images/star-rating.png"
            alt="Star Rating"
            width={500}
            height={500}
            className={`x-auto absolute bottom-[48px] h-[24px] w-auto object-cover`}
          />
        </div>
        {/* Review Item Section */}
        <div
          className={`relative flex h-[420px] w-[18vw] flex-col items-center gap-y-6 rounded-[16px] bg-[#FDFDFD] px-8 py-10`}
        >
          <Icon
            color={'white'}
            name={'airdrop'}
            className={`
              absolute
              left-[48px]
              top-[48px]
              opacity-[.03]
              [&>*]:fill-[#21409A]
            `}
            width={194}
            height={194}
          />
          <p className={`text-[20px] font-light text-[#58595B]`}>
            “I use this as both a parent and a teacher. I love being able to
            keep track of my child’s day.”
          </p>
          <p
            className={`x-auto absolute bottom-[96px] text-[16px] font-bold text-[#58595B]`}
          >
            Eliza,
            <br />
            <span className={`text-[14px] font-normal`}>
              Mom of 4 years old girl
            </span>
          </p>
          <Image
            src="/assets/images/star-rating.png"
            alt="Star Rating"
            width={500}
            height={500}
            className={`x-auto absolute bottom-[48px] h-[24px] w-auto object-cover`}
          />
        </div>
        {/* Review Item Section */}
        <div
          className={`relative flex h-[420px] w-[18vw] flex-col items-center gap-y-6 rounded-[16px] bg-[#FDFDFD] px-8 py-10`}
        >
          <Icon
            color={'white'}
            name={'airdrop'}
            className={`
              absolute
              left-[48px]
              top-[48px]
              opacity-[.03]
              [&>*]:fill-[#21409A]
            `}
            width={194}
            height={194}
          />
          <p className={`text-[20px] font-light text-[#58595B]`}>
            “Love using this as a teacher! It’s intuitive and easy to learn. I
            love communicating with parents directly through the app.”
          </p>
          <p
            className={`x-auto absolute bottom-[96px] text-[16px] font-bold text-[#58595B]`}
          >
            Tammy,
            <br />
            <span className={`text-[14px] font-normal`}>
              Mom of 2 years old girl
            </span>
          </p>
          <Image
            src="/assets/images/star-rating.png"
            alt="Star Rating"
            width={500}
            height={500}
            className={`x-auto absolute bottom-[48px] h-[24px] w-auto object-cover`}
          />
        </div>
      </div>
    </div>
  );
};

export default CustomerReview;
