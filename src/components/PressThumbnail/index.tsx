import Image from 'next/image';
import Link from 'next/link';

const PressThumbnail = () => {
  return (
    <div className={`w-full`}>
      <Image
        src="/assets/images/TechCrunch.png"
        alt="Help Banner Images"
        width={500}
        height={500}
        className={`mb-8 h-[160px] w-full object-cover`}
      />
      <div className={`flex flex-col`}>
        <div className={`mb-6 flex flex-row gap-x-4`}>
          <p
            className={`rounded-[4px] border-[1px] border-[#58595B] px-6 py-2 text-[14px] text-[#58595B]`}
          >
            June 22, 2023 14:25
          </p>
        </div>
        <p className={`mb-4 text-[18px] font-bold text-[#58595B]`}>
          Mầm Chồi Lá $10 Million to Keep Parents In-the-Know About their Kids’
          Day at School
        </p>
        <p className={`mb-4 text-[18px] font-bold text-[#21409A]`}>
          TechCrunch
        </p>
        <Link href="/" className={`flex text-[14px] text-[#58595B] underline`}>
          Read full article
        </Link>
      </div>
    </div>
  );
};

export default PressThumbnail;
