import { yupResolver } from '@hookform/resolvers/yup';
import { useRouter } from 'next/router';
import { useCallback } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';

import Button from '@/components/Button';
import Icon from '@/components/Icon';
import Input from '@/components/Input';
import Title from '@/components/Title';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import type { ILanguageInput } from '@/typings/common';

interface FormValues {
  schoolName?: string;
  schoolDirectorName?: string;
  schoolEmail?: string;
  schoolHotline?: string;
  schoolStudentQuantity?: string;
  email?: string;
}

const DATA = {
  title: {
    en: 'Invite your preschool or child care',
    vi: 'Giới thiệu cho trường học của bạn',
  },
};

const SuggestForm = () => {
  const locale = useRouter().locale as keyof ILanguageInput;
  const trans = useTrans();

  const FormSchema = yup.object().shape({
    schoolName: yup.string().required(trans.fieldIsRequired.schoolName),
    schoolDirectorName: yup
      .string()
      .required(trans.fieldIsRequired.schoolDirectorName),
    schoolEmail: yup
      .string()
      .email(trans.incorrectFormat)
      .required(trans.fieldIsRequired.schoolEmail),
    schoolHotline: yup
      .string()
      .trim()
      .matches(/^[0-9]*$/, trans.incorrectFormat)
      .required(trans.fieldIsRequired.schoolHotline),
    schoolStudentQuantity: yup
      .string()
      .trim()
      .matches(/^[0-9]*$/, trans.incorrectFormat)
      .required(trans.fieldIsRequired.schoolStudentQuantity),
    email: yup
      .string()
      .email(trans.incorrectFormat)
      .required(trans.fieldIsRequired.yourEmail),
  });

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<FormValues>({
    // @ts-ignore
    resolver: yupResolver(FormSchema),
    defaultValues: {
      schoolName: '',
      schoolDirectorName: '',
      schoolEmail: '',
      schoolHotline: '',
      schoolStudentQuantity: '',
      email: '',
    },
  });

  const handleSubmitForm = useCallback((data: FormValues) => {
    console.log('data', data);
  }, []);

  // @ts-ignore
  return (
    <section className={sectionClassName}>
      <div
        className={`flex flex-col items-center rounded-2xl bg-light/30 p-20 lg:p-12 md:p-6`}
      >
        <Title text={DATA.title[locale]} />

        <div
          className={
            'mb-12 mt-16 grid w-full max-w-[772px] grid-cols-2 gap-6 md:mt-8 md:grid-cols-1'
          }
        >
          <Controller
            control={control}
            name={'schoolName'}
            render={({ field: { onChange, value } }) => (
              <Input
                label={trans.yourChildSchoolName}
                value={value}
                onChange={onChange}
                error={errors?.schoolName && errors?.schoolName?.message}
              />
            )}
          />
          <Controller
            control={control}
            name={'schoolDirectorName'}
            render={({ field: { onChange, value } }) => (
              <Input
                label={trans.schoolDirectorName}
                value={value}
                onChange={onChange}
                error={
                  errors?.schoolDirectorName &&
                  errors?.schoolDirectorName?.message
                }
              />
            )}
          />
          <Controller
            control={control}
            name={'schoolEmail'}
            render={({ field: { onChange, value } }) => (
              <Input
                label={trans.schoolEmail}
                value={value}
                onChange={onChange}
                error={errors?.schoolEmail && errors?.schoolEmail?.message}
              />
            )}
          />
          <Controller
            control={control}
            name={'schoolHotline'}
            render={({ field: { onChange, value } }) => (
              <Input
                label={trans.schoolHotline}
                value={value}
                onChange={onChange}
                error={errors?.schoolHotline && errors?.schoolHotline?.message}
              />
            )}
          />
          <Controller
            control={control}
            name={'schoolStudentQuantity'}
            render={({ field: { onChange, value } }) => (
              <Input
                label={trans.schoolStudentQuantity}
                value={value}
                onChange={onChange}
                error={
                  errors?.schoolStudentQuantity &&
                  errors?.schoolStudentQuantity?.message
                }
              />
            )}
          />
          <Controller
            control={control}
            name={'email'}
            render={({ field: { onChange, value } }) => (
              <Input
                label={trans.yourEmail}
                value={value}
                onChange={onChange}
                error={errors?.email && errors?.email?.message}
              />
            )}
          />
        </div>

        <Button
          className={'rounded-[30px]'}
          onClick={handleSubmit((data) => handleSubmitForm(data))}
        >
          <>
            <span>{trans.inviteYourSchool}</span>
            <Icon name={'export-circle'} className={'fill-white'} />
          </>
        </Button>
      </div>
    </section>
  );
};

export default SuggestForm;
