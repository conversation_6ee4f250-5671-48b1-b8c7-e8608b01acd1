import Icon from '@/components/Icon';

const BlogFilter = () => {
  return (
    <div
      className={`items-normal mx-auto mb-[88px] flex h-full w-full max-w-[1728px] px-24`}
    >
      <div
        className={`relative flex w-full flex-row items-start justify-between px-24`}
      >
        <div className={`flex items-center`}>
          <Icon
            color={''}
            name={'clock'}
            className={`mr-4`}
            width={32}
            height={32}
          />
          <input
            type="text"
            placeholder="Blog Search"
            className={`bg-transparent text-[24px]`}
          />
        </div>
        <div className={`flex flex-row gap-x-8`}>
          <p className={`text-[24px] text-[#58595B]`}>Filter by Category:</p>
          <div className={`flex max-w-[35vw] flex-wrap gap-4`}>
            <p
              className={`rounded-full bg-[#21409A] px-6 py-2 text-[14px] text-[#FDFDFD]`}
            >
              Category
            </p>
            <p
              className={`rounded-full bg-[#21409A] px-6 py-2 text-[14px] text-[#FDFDFD]`}
            >
              Category
            </p>
            <p
              className={`rounded-full bg-[#21409A] px-6 py-2 text-[14px] text-[#FDFDFD]`}
            >
              Category
            </p>
            <p
              className={`rounded-full bg-[#21409A] px-6 py-2 text-[14px] text-[#FDFDFD]`}
            >
              Category
            </p>
            <p
              className={`rounded-full bg-[#21409A] px-6 py-2 text-[14px] text-[#FDFDFD]`}
            >
              Category
            </p>
            <p
              className={`rounded-full bg-[#21409A] px-6 py-2 text-[14px] text-[#FDFDFD]`}
            >
              Category
            </p>
            <p
              className={`rounded-full bg-[#21409A] px-6 py-2 text-[14px] text-[#FDFDFD]`}
            >
              Category
            </p>
            <p
              className={`rounded-full bg-[#21409A] px-6 py-2 text-[14px] text-[#FDFDFD]`}
            >
              Category
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogFilter;
