import type { FC, InputHTMLAttributes } from 'react';
import React from 'react';
import SelectReact from 'react-select';

import Icon from '@/components/Icon';
import TextError from '@/components/TextError';

export interface ISelectOption {
  value?: string | number;
  label?: string;
}

interface ISelectProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  value?: any;
  isDisabled?: boolean;
  isMulti?: boolean;
  placeholder?: string;
  required?: boolean;
  options: ISelectOption[];
  error?: string | undefined;
  classNamePrefix?: string;
  className?: string;
  isSmall?: boolean;
  onChange?: ((evt: any) => void) | undefined;
}

const Index: FC<ISelectProps> = ({
  label,
  placeholder,
  required = true,
  options,
  error = '',
  value,
  isDisabled = false,
  isMulti = false,
  classNamePrefix = '',
  className = '',
  isSmall,
  onChange = () => {},
}) => {
  const customStyles = {
    control: (base: any) => ({
      ...base,
      flexDirection: 'row',
      width: '100%',
      minHeight: isSmall ? '48px' : '56px',
      height: 'auto',
      backgroundColor: 'rgba(218, 221, 231, 0.3)',
      border: 'none',
      borderRadius: '8px',
      fontSize: '16px',
      fontWeight: '400',
      lineHeight: '20px',
      textAlign: 'left',
      padding: '0 16px 0 12px',
      boxShadow: 'none',
      cursor: 'pointer',
      ':hover': {
        boxShadow: 'none',
      },
    }),
    singleValue: (styles: any) => ({
      ...styles,
      color: 'rgba(88, 89, 91, 1)',
    }),
    option: (provided: any, { isSelected, isFocused }: any) => ({
      ...provided,
      // eslint-disable-next-line no-nested-ternary
      backgroundColor: isSelected
        ? 'rgba(33, 64, 154, 1)'
        : isFocused
          ? 'rgba(33, 64, 154, 0.1)'
          : 'transparent',
      cursor: 'pointer',
    }),
    menu: (provided: any) => ({
      ...provided,
      background: '#ffffff',
      zIndex: 99,
    }),
    indicatorSeparator: () => ({ display: 'none' }),
    placeholder: (defaultStyles: any) => {
      return {
        ...defaultStyles,
        color: '#999',
      };
    },
  };

  return (
    <div
      className={`relative text-left ${
        isDisabled ? 'cursor-not-allowed opacity-50' : ''
      } ${className}`}
    >
      {label && (
        <h4 className={'mb-2 text-blue'}>
          {label} {required && <span className={'text-red'}>*</span>}
        </h4>
      )}
      <SelectReact
        isDisabled={isDisabled}
        isMulti={isMulti}
        classNamePrefix={classNamePrefix}
        value={value}
        options={options}
        placeholder={placeholder}
        styles={customStyles}
        onChange={onChange}
        components={{
          DropdownIndicator: () => (
            <Icon name="triangle-down" className="fill-primary" />
          ),
        }}
      />
      {error && <TextError text={error} />}
    </div>
  );
};

export default Index;
