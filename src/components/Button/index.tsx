import Link from 'next/link';

interface IProps {
  children: any;
  outline?: boolean;
  disabled?: boolean;
  res?: any;
  url?: string;
  className?: string;
  target?: string;
  onClick?: () => void;
}

const Button = ({
  children,
  outline = false,
  disabled = false,
  url = '',
  className = '',
  onClick,
  target = '',
}: IProps) => {
  const outlineClassName = `border-1 border-blue bg-transparent text-blue`;
  const classNameList = `flex w-fit h-14 items-center justify-center rounded-lg px-8 gap-x-2 whitespace-nowrap font-bold text-18 md:text-16 md:h-[52px] md:px-6 md:rounded-lg 
  ${outline ? outlineClassName : 'bg-blue text-white'}
  ${disabled ? 'opacity-50 cursor-not-allowed' : ''} 
  ${className}`;

  return url ? (
    <Link className={classNameList} href={url} target={target}>
      {children}
    </Link>
  ) : (
    <button
      type={'button'}
      className={classNameList}
      disabled={disabled}
      onClick={onClick}
    >
      {children}
    </button>
  );
};

export default Button;
