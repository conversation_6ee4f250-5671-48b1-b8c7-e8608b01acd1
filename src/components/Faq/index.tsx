import { useRouter } from 'next/router';
import { useCallback, useState } from 'react';

import Icon from '@/components/Icon';
import TextGradient from '@/components/TextGradient';
import { sectionClassName } from '@/constants';
import type { ILanguageInput } from '@/typings/common';
import { formatDescription } from '@/utils';

const FAQS = [
  {
    id: 1,
    question: {
      en: 'Question 1',
      vi: 'Câu hỏi 1',
    },
    answer: {
      en: 'Answer 1',
      vi: 'Câu trả lời 1',
    },
  },
  {
    id: 2,
    question: {
      en: 'Question 2',
      vi: 'Câu hỏi 2',
    },
    answer: {
      en: 'Answer 2',
      vi: 'Câu trả lời 2',
    },
  },
];

const FaqList = ({ className }: { className?: string }) => {
  const locale = useRouter().locale as keyof ILanguageInput;
  const [faqsSelected, setFaqsSelected] = useState<number[]>([]);

  const toggleFaq = useCallback(
    (faqId: number) => {
      if (faqsSelected.includes(faqId)) {
        const newFaqIdSelected = faqsSelected.filter((id) => id !== faqId);
        setFaqsSelected(newFaqIdSelected);
      } else {
        setFaqsSelected([...faqsSelected, faqId]);
      }
    },
    [faqsSelected],
  );

  return (
    <section
      className={`${sectionClassName} mt-[168px] lg:mt-[90px] md:mt-0 ${className}`}
    >
      <h1
        className={
          'text-center text-72 font-normal lg:text-48 md:text-left md:text-40'
        }
      >
        {locale === 'en' ? (
          <>
            <TextGradient text={'Frequently'} className={'inline'} /> asked
            questions.
          </>
        ) : (
          <>
            Các câu hỏi{' '}
            <TextGradient text={'thường gặp'} className={'inline'} />
          </>
        )}
      </h1>
      <div className={'mt-21 lg:mt-10 md:mt-6'}>
        {FAQS?.map((item: any) => {
          const isSelected = faqsSelected?.includes(item.id);
          return (
            <div key={item.id} className={'border-b-1 border-b-natreul pb-3'}>
              <div className={'flex items-center justify-between gap-x-2 pt-6'}>
                <b>{item.question?.[locale as keyof string]}</b>
                <button onClick={() => toggleFaq(item.id)}>
                  <Icon
                    name={isSelected ? 'minus-circle' : 'add-circle'}
                    className={'fill-dark'}
                  />
                </button>
              </div>
              {isSelected && (
                <div className={`mt-6 overflow-hidden md:text-12`}>
                  {formatDescription(item.answer?.[locale as keyof string])}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </section>
  );
};

export default FaqList;
