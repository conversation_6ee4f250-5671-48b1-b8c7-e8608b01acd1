import Link from 'next/link';

import Icon from '@/components/Icon';

interface IProps {
  href: string;
  text: string;
  className?: string;
  type?: 'primary' | 'light' | 'dark';
  target?: string;
  iconHidden?: boolean;
}
const AppLink = ({
  href,
  text,
  className,
  type = 'dark',
  target = '',
  iconHidden = false,
}: IProps) => {
  const TYPES: any = {
    primary: {
      text: 'text-primary',
      border: 'border-primary',
      icon: 'fill-primary',
    },
    dark: {
      text: 'text-dark',
      border: 'border-dark',
      icon: 'fill-dark',
    },
    light: {
      text: 'text-light',
      border: 'border-light',
      icon: 'fill-light',
    },
  };
  return (
    <Link
      href={href}
      target={target}
      className={`flex w-fit items-center gap-x-2 ${TYPES[type].text} ${className}`}
    >
      <span className={`underline lg:text-16 ${TYPES[type].border}`}>
        {text}
      </span>
      {!iconHidden && (
        <Icon name={'export'} size={20} className={TYPES[type].icon} />
      )}
    </Link>
  );
};

export default AppLink;
