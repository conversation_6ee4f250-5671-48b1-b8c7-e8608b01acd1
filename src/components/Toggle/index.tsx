import React from 'react';

import TextError from '@/components/TextError';

interface IProps {
  label?: string;
  data: {
    label: string;
    value: string | number | undefined | boolean;
  }[];
  value: string | number | undefined | boolean;
  onChange: (value: string | number | undefined | boolean) => void;
  error?: string;
  disabled?: boolean;
  itemClassName?: string;
}
const Toggle = ({
  label,
  data,
  value,
  disabled,
  onChange,
  error,
  itemClassName = '',
}: IProps) => {
  return (
    <div className={disabled ? 'opacity-50' : ''}>
      {label && (
        <h4 className={'mb-2 text-blue'}>
          {label} <span className={'text-red'}>*</span>
        </h4>
      )}
      <div className={'flex flex-wrap gap-4'}>
        {data.map((item) => (
          <button
            disabled={disabled}
            key={item?.label}
            className={`flex h-14 min-w-[120px] flex-1 items-center rounded-lg px-6 md:w-[50%] md:min-w-[unset] ${
              value === item?.value
                ? 'bg-blue text-white'
                : 'bg-natreul/30 text-dark/[0.56]'
            } ${itemClassName}`}
            onClick={() => onChange(item?.value)}
          >
            <span>{item?.label}</span>
          </button>
        ))}
      </div>
      {error && <TextError text={error} />}
    </div>
  );
};

export default Toggle;
