import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { animateScroll as scroll } from 'react-scroll';

import REGION_DATA from '@/constants/regions';
import type { MultipleLanguage } from '@/typings/common';

export const handleScrollingTo = (position: number) => {
  scroll.scrollTo(position, {
    delay: 0,
    smooth: 'linear',
    duration: 500,
  });
};

export const formatDescription = (description: string) => {
  return description?.replaceAll('\n', '<br />');
};

export const convertDataToSelectModel = (data: any) => {
  return data?.map((item: any) => {
    return {
      value: item.id,
      label: item.name,
      districts: [],
    };
  });
};

export const getProvinces = () => {
  return REGION_DATA;
};

export const formatDate = (
  date: string | undefined | Dayjs,
  format = 'DD MMMM, YYYY',
) => {
  if (!date) return '';
  return dayjs(date).format(format);
};

export const getBlogContentByLanguage = (
  content: MultipleLanguage | undefined,
  locale = 'en',
) => {
  if (locale === 'en') {
    return content?.en || content?.vi || '';
  }

  if (locale === 'vi') {
    return content?.vi || content?.en || '';
  }
  return '';
};

export const convertToSlug = (text?: string) => {
  if (!text) return '';
  let str = text.replace(/^\s+|\s+$/g, '').toLowerCase(); // trim

  // remove accents, swap ñ for n, etc
  const from =
    'ạàáảãâậẩẫấầăặẵẳắằẽèéëêệềếểễìíỉĩịọòóõỏôộồốỗổơợờớởỡụùúủũưựứừửữđ·/_,:;';
  const to =
    'aaaaaaaaaaaaaaaaaeeeeeeeeeeiiiiiooooooooooooooooouuuuuuuuuuud------';
  // @ts-ignore
  for (let i = 0, l = from.length; i < l; i += 1) {
    str = str.replace(new RegExp(from.charAt(i), 'g'), to.charAt(i));
  }

  return str
    .replace(/[^a-z0-9 -]/g, '') // remove invalid chars
    .replace(/\s+/g, '-') // collapse whitespace and replace by -
    .replace(/-+/g, '-'); // collapse dashes
};

export const formatMeta = (seoData: any, locale: string) => {
  return {
    title: !!seoData?.title && seoData?.title?.[locale],
    description: !!seoData?.description && seoData?.description?.[locale],
    keywords: !!seoData?.keywords && seoData?.keywords?.[locale],
    images: !!seoData?.image?.url && [
      {
        url: seoData?.image?.url || '',
      },
    ],
  };
};
