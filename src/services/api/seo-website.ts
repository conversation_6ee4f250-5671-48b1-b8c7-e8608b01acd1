import qs from 'qs';

export const getSeoWebsites = (headers = {}) => {
  const query = qs.stringify(
    {
      populate: [
        'page',
        'title',
        'description',
        'keywords',
        'image',
        'pageName',
      ],
      pagination: {
        page: 1,
        pageSize: 30,
      },
    },
    {
      encodeValuesOnly: true,
    },
  );
  return fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/api/seo-landing-pages/?${query}`,
    {
      headers,
    },
  );
};
