import qs from 'qs';

import axios from '../axios';

interface IPaginationPayload {
  page: number;
  pageSize: number;
}

export const getNews = (
  pagination: IPaginationPayload,
  filters?: any,
  headers = {},
) => {
  const query = qs.stringify({
    filters,
    populate: [
      'category.name',
      'category.parent',
      'title',
      'content',
      'thumbnail',
    ],
    pagination,
    sort: ['createdAt:DESC'],
  });
  return axios.get(`/news-landings?${query}`, { headers });
};

export const getNewsById = (id: string) => {
  const query = qs.stringify({
    populate: [
      'category',
      'category.parent',
      'content',
      'title',
      'slug',
      'thumbnail',
    ],
    // publicationState: "preview",
  });
  return axios.get(`/news-landings/${id}?${query}`);
};

export const fetchNews = (
  pagination: IPaginationPayload,
  filters: any,
  headers = {},
) => {
  const query = qs.stringify(
    {
      filters: {
        ...filters,
        status: 'published',
      },
      populate: [
        'category',
        'category.name',
        'category.parent',
        'content',
        'title',
        'slug',
        'thumbnail',
      ],
      pagination,
      publicationState: 'preview',
      sort: ['createdAt:DESC'],
    },
    {
      encodeValuesOnly: true,
    },
  );
  return fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/api/news-landings?${query}`,
    {
      headers,
    },
  );
};

export const fetchNewsById = (id: string) => {
  const query = qs.stringify({
    populate: [
      'category',
      'category.name',
      'category.parent',
      'category.parent.name',
      'content',
      'title',
      'slug',
      'thumbnail',
    ],
    publicationState: 'preview',
  });
  return fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/api/news-landings/${id}?${query}`,
  );
};
