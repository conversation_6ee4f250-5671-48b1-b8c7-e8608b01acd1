import type { AxiosError } from 'axios';
import axios from 'axios';

const instance = axios.create({
  baseURL: `${process.env.NEXT_PUBLIC_API_URL}/api`,
  timeout: 60000,
});

// Add a request interceptor
instance.interceptors.request.use(
  (config) => {
    // if (token && config.headers) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    // config.headers['Content-Type'] = 'application/json';
    return config;
  },
  (error) => {
    Promise.reject(error);
  },
);

instance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error: AxiosError) => {
    if (error?.response?.status === 401) {
      return Promise.reject(error);
    }

    if (error?.response?.status === 401) {
      // handle refresh token
    }
    return Promise.reject(error);
  },
);

export default instance;
