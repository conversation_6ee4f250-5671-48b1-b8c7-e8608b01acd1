import type { ReactNode } from 'react';
import React from 'react';

import Footer from '@/components/Footer';
import Header from '@/components/Header';

type IMainProps = {
  meta: ReactNode;
  children: ReactNode;
  className?: string;
};

const Main = (props: IMainProps) => {
  // const router = useRouter();
  // const [windowWidth, setWindowWidth] = useState(0);

  // useEffect(() => {
  //   setWindowWidth(window.innerWidth);
  //   const handleResize = () => {
  //     setWindowWidth(window.innerWidth);
  //   };
  //
  //   // Attach the event listener to update the window width on resize
  //   window.addEventListener('resize', handleResize);
  //
  //   // Clean up the event listener on component unmount
  //   return () => {
  //     window.removeEventListener('resize', handleResize);
  //   };
  // }, []);

  // if (windowWidth < 768 && router.pathname !== 'sale-proposal')
  //   return (
  //     <div className={'h-screen bg-secondaryGradient'}>
  //       <div
  //         className={
  //           'flex h-full flex-col items-center justify-center gap-y-4 p-4'
  //         }
  //       >
  //         <Icon
  //           name={'logo'}
  //           className={'mb-[112px] fill-primary'}
  //           width={230}
  //           height={64}
  //         />
  //         <h5 className={`barlow text-14 text-red underline`}>
  //           <b>Our website is building responsive version</b>
  //         </h5>
  //         <h2 className={'mb-12 text-48 font-normal'}>
  //           Its coming{' '}
  //           <TextGradient text={'soon.'} className={'inline !text-48'} />
  //         </h2>
  //         <p>Please view our website on Desktop version</p>
  //       </div>
  //     </div>
  //   );

  return (
    <>
      {props.meta}
      <div
        className={`relative h-full w-full bg-secondaryGradient ${props?.className}`}
      >
        <Header />
        <main className={'pt-[200px] lg:pt-[160px] md:pt-[120px]'}>
          {props.children}
        </main>
        <Footer />
      </div>
    </>
  );
};

export { Main };
