@import 'rc-slider/assets/index.css';
@import "slick-carousel/slick/slick.css";
@import "slick-carousel/slick/slick-theme.css";
@import "~react-datepicker/dist/react-datepicker.min.css";
@import "~react-toastify/dist/ReactToastify.css";

@tailwind base;
@tailwind components;
@tailwind utilities;
@tailwind variants;

@font-face {
  font-family: "GoogleSans-Bold";
  font-display: block;
  src: url("../../public/assets/fonts/GoogleSans-Bold.otf");
}
@font-face {
  font-family: "GoogleSans-BoldItalic";
  font-display: block;
  src: url("../../public/assets/fonts/GoogleSans-BoldItalic.otf");
}
@font-face {
  font-family: "GoogleSans-Italic";
  font-display: block;
  src: url("../../public/assets/fonts/GoogleSans-Italic.otf");
}
@font-face {
  font-family: "GoogleSans-Medium";
  font-display: block;
  src: url("../../public/assets/fonts/GoogleSans-Medium.otf");
}
@font-face {
  font-family: "GoogleSans-MediumItalic";
  font-display: block;
  src: url("../../public/assets/fonts/GoogleSans-MediumItalic.otf");
}
@font-face {
  font-family: "GoogleSans-Regular";
  font-display: block;
  src: url("../../public/assets/fonts/GoogleSans-Regular.otf");
}

body {
  @apply font-GoogleSans-Regular text-16 text-dark md:text-14;

  &::-webkit-scrollbar {
    display: none;
  }
}

.barlow {
  @apply font-barlow
}

.barlow--bold {
  @apply font-barlow;
  font-weight: 700;
}

@layer base {
  .font-bold, b, strong, h1, h2, h3, h4 {
    @apply font-GoogleSans-Bold;
  }

  .font-normal {
    @apply font-GoogleSans-Regular;
  }
}

.slick-slide:not(.slick-current) {
  opacity: 0.1;
}

input, textarea {
  resize: none;

  &:focus {
    outline: none;
  }
}

.hide-scrollbar {
  &::-webkit-scrollbar {
    display: none; // Chrome, Safari, Opera
  }
}

// Datepicker
.react-datepicker-popper {
  z-index: 99;
}

.react-datepicker-wrapper {
  @apply w-full relative z-10;
}

.react-datepicker {
  &__input-container {
    input {
      width: 100%;
      height: 56px;
      padding-left: 24px;
      outline: none;
      border-radius: 8px;
    }
  }

  &__triangle {
    &::before,
    &::after {
      right: 16px;
      left: auto !important;
    }
  }

  &__day-name,
  &__day {
    width: 2.5rem;
    line-height: 2.5rem;
  }
}

#react-tiny-popover-container {
  z-index: 30;
}
