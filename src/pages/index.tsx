import classNames from 'classnames';
import { useRouter } from 'next/router';
import React, {
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import Slider from 'react-slick';
import YouTube from 'react-youtube';

import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Button from '@/components/Button';
import CustomerSupport from '@/components/Customer';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import { sectionClassName } from '@/constants';
import { AppContext } from '@/context/context';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { getSeoWebsites } from '@/services/api/seo-website';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';
import { formatMeta, handleScrollingTo } from '@/utils';
import { getErrorMessage } from '@/utils/error';

const SLIDER = [
  {
    title: (locale: string) => (
      <>{locale === 'en' ? 'Parenting' : 'Nuôi dạy'}</>
    ),
    image: 'request-demo.png',
    card: {
      image: 'parent-app-module.png',
      text: 'parentsApplication',
      url: '/parents-application',
    },
  },
  {
    title: (locale: string) => (
      <>{locale === 'en' ? 'Education' : 'Giáo dục'}</>
    ),
    image: 'request-demo.png',
    card: {
      image: 'educator-app-module.png',
      text: 'educatorsApplication',
      url: '/educators-application',
    },
  },
  {
    title: (locale: string) => (
      <>{locale === 'en' ? 'Management' : 'Quản lý'}</>
    ),
    image: 'request-demo.png',
    card: {
      image: 'principal-system-module.png',
      text: 'principalsManagement',
      url: '/principals-management',
    },
  },
];

const FEATURES = [
  {
    icon: 'clock',
    title: {
      en: () => (
        <>
          More <b>time</b>
        </>
      ),
      vi: () => (
        <>
          Tiết kiệm <b>thời gian</b>
        </>
      ),
    },
    description: {
      en: () => (
        <>
          Add 10 days to every month by <b>automating manual work</b> from
          inquiry to retention.
        </>
      ),
      vi: () => (
        <>
          Tiết kiệm trung bình 10 ngày làm việc mỗi tháng bằng{' '}
          <b>tự động hoá công việc</b> từ hành chính đến tương tác phụ huynh.
        </>
      ),
    },
  },
  {
    icon: 'home-trend-up',
    title: {
      en: () => (
        <>
          Higher <b>enrollment</b>
        </>
      ),
      vi: () => (
        <>
          Đẩy nhanh <b>tuyển sinh</b>
        </>
      ),
    },
    description: {
      en: () => (
        <>
          Consistently <b>hit enrollment targets</b> with lead conversion rates
          as high as 75%.
        </>
      ),
      vi: () => (
        <>
          Đảm bảo <b>đạt mục tiêu tuyển sinh</b> với tỷ lệ chuyển đổi lên đến
          75% bằng quy trình nhanh chóng.
        </>
      ),
    },
  },
  {
    icon: 'lovely',
    title: {
      en: () => (
        <>
          Better <b>retention</b>
        </>
      ),
      vi: () => (
        <>
          Cải thiện <b>tương tác</b>
        </>
      ),
    },
    description: {
      en: () => (
        <>
          Increase family and staff retention with our{' '}
          <b>mobile engagement app.</b>
        </>
      ),
      vi: () => (
        <>
          Tăng khả năng tương tác của Phụ huynh và Nhân viên bằng{' '}
          <b>ứng dụng di động</b> Mầm Chồi Lá.
        </>
      ),
    },
  },
  {
    icon: 'folder-open',
    title: {
      en: () => (
        <>
          Simple <b>management</b>
        </>
      ),
      vi: () => (
        <>
          Đơn giản hoá <b>quản lý</b>
        </>
      ),
    },
    description: {
      en: () => (
        <>
          Streamline <b>childcare operations</b>, payment processing, and
          compliance reporting.
        </>
      ),
      vi: () => (
        <>
          Tối ưu hoá việc <b>vận hành trung tâm giáo dục</b>, xử lý các thanh
          toán, và cập nhật các quy định & tuân thủ.
        </>
      ),
    },
  },
];

const OUTSTANDING_FEATURES: any = [
  {
    title: {
      vi: '01. Website trường học',
      en: '01. School Website',
    },
    subTitle: {
      vi: 'Tự Tay Xây Dựng Website Trường Học Dễ Dàng',
      en: 'Build a School Website Easily Yourself',
    },
    description: {
      vi: 'Chỉ vài phút để có ngay một website trường học đẹp lung linh. Platform thân thiện của Mầm Chồi Lá cho phép bạn tự tùy chỉnh nội dung, cập nhật thông tin quan trọng và phô diễn tinh thần độc đáo của trường, không cần chuyên môn kỹ thuật.',
      en: "Just a few minutes to get a sparkling beautiful school website right away. Mam Choi La's user-friendly platform allows you to customize content, update important information, and showcase the unique spirit of the school, without the need for technical expertise.",
    },
    image: 'school-website.png',
  },
  {
    title: {
      vi: '02. Tuyển sinh trực tuyến',
      en: '02. Online enrollment',
    },
    subTitle: {
      vi: 'Đăng Ký Trực Tuyến, Tiện Lợi Từng Bước',
      en: 'Online Registration, Convenient Step by Step',
    },
    description: {
      vi: 'Quên đi cảnh tượng nộp đơn giấy rườm rà. Hệ thống đăng ký trực tuyến của Mầm Chồi Lá giúp quá trình tuyển sinh trở nên đơn giản, cho phép phụ huynh nộp đơn bất cứ lúc nào, bất cứ nơi đâu và theo dõi trạng thái hồ sơ một cách chính xác.',
      en: "Forget the hassle of paper application scenes. Mam Choi La's online registration system makes the enrollment process simple, allowing parents to submit applications anytime, anywhere, and accurately track the status of their documents.",
    },
    image: 'online-enrollment.png',
  },
  {
    title: {
      vi: '03. Quản lý Học sinh',
      en: '03. Student Management',
    },
    subTitle: {
      vi: 'Quản Lý Học Sinh Chính Xác, Dễ Dàng',
      en: 'Accurate, Easy Student Management',
    },
    description: {
      vi: 'Mọi thông tin học sinh đều được tổ chức gọn gàng và dễ truy cập. Từ điểm số và điểm danh đến hồ sơ sức khỏe và ghi chép hành vi, hệ thống toàn diện của Mầm Chồi Lá đảm bảo Phụ Huynh luôn nắm bắt đầy đủ dữ liệu cần thiết của học sinh.',
      en: "All student information is neatly organized and easily accessible. From grades and attendance records to health profiles and behavioral notes, Mầm Chồi Lá's comprehensive system ensures that parents always have access to complete student data.",
    },
    image: 'student-management.png',
    subFeatures: [
      {
        vi: 'Kiểm tra lịch sử đóng học phí',
        en: 'Check tuition fee payment history',
      },
      {
        vi: 'Kiểm tra lịch sử điểm danh',
        en: 'Check attendance history',
      },
    ],
  },
  {
    title: {
      vi: '04.Cấu hình mẫu Học phí',
      en: '04. Fee Template Configuration',
    },
    subTitle: {
      vi: 'Tự Do Tuỳ Chỉnh Mẫu Hóa Đơn Học Phí',
      en: 'Customize Invoice Templates Freely',
    },
    description: {
      vi: 'Tạo lập một lần và để hệ thống lo liệu với mẫu hóa đơn học phí tự động. Dù trường bạn cần tính toán linh hoạt thế nào, hệ thống của Mầm Chồi Lá cũng đảm bảo mọi hóa đơn đều chính xác, rõ ràng và đúng hạn!',
      en: "Set up once and let the system handle it with automatic fee invoice templates. No matter how flexible your school's calculations need to be, Mầm Chồi Lá's system ensures that all invoices are accurate, clear, and timely!",
    },
    image: 'fee-template-configuration.png',
    subFeatures: [
      {
        vi: 'Tuỳ chỉnh công thức',
        en: 'Customize formulas',
      },
      {
        vi: 'Gắn thẻ / nhãn nhóm học sinh',
        en: 'Tag / Label student groups',
      },
      {
        vi: 'Cài đặt quy trình tự động tạo & gửi hoá đơn học phí',
        en: 'Set up automatic invoice creation & sending process',
      },
    ],
  },
  {
    title: {
      vi: '05. Quản lý Học phí tự động',
      en: '05. Automatic Fee Management',
    },
    subTitle: {
      vi: 'Tự Động Hóa Việc Tính Tiền Học Phí',
      en: 'Automate Fee Calculation',
    },
    description: {
      vi: 'Giảm bớt công việc hành chính với hóa đơn tự động. Hệ thống của Mầm Chồi Lá gửi hóa đơn kịp thời, xử lý thanh toán và theo dõi tất cả các giao dịch tài chính, đảm bảo sự chính xác và yên tâm.',
      en: "Reduce administrative work with automatic invoices. Mầm Chồi Lá's system sends invoices promptly, handles payments, and tracks all financial transactions, ensuring accuracy and peace of mind.",
    },
    image: 'automatic-fee-management.png',
  },
  {
    title: {
      vi: '06. Điểm danh tự động',
      en: '06. Automatic Attendance',
    },
    subTitle: {
      vi: 'Điểm Danh Nhanh Chóng và An Toàn với Camera AI',
      en: 'Quick and Safe Attendance with AI Camera',
    },
    description: {
      vi: 'Nâng cao an toàn và hiệu quả với hệ thống điểm danh bằng camera AI. Học sinh chỉ cần đi qua camera, không cần thẻ từ. Quá trình nhanh chóng, đáng tin cậy, an toàn, giúp mỗi buổi sáng trở nên suôn sẻ hơn.',
      en: 'Enhance safety and efficiency with AI camera attendance system. Students simply walk past the camera, no need for cards. Fast, reliable, and safe process makes every morning smoother.',
    },
    image: 'automatic-attendance.png',
    subFeatures: [
      {
        vi: 'Điểm danh học sinh',
        en: 'Student Attendance',
      },
      {
        vi: 'Điểm danh nhân viên, giáo viên',
        en: 'Staff, Teacher Attendance',
      },
      {
        vi: 'Phát hiện người lạ',
        en: 'Detecting Strangers',
      },
    ],
  },
  {
    title: {
      vi: '07. Quản lý Giáo viên, Nhân viên',
      en: '07. Teacher, Staff Management',
    },
    subTitle: {
      vi: 'Quản Lý Nhân Sự Hiệu Quả với Công Cụ Đồng Bộ',
      en: 'Efficient Human Resource Management with Synchronization Tool',
    },
    description: {
      vi: 'Tổ chức hồ sơ nhân viên, theo dõi lịch trình và quản lý tiền lương một cách liền mạch. Công cụ tích hợp của Mầm Chồi Lá giúp bạn duy trì một đội ngũ nhân viên hiệu quả, tập trung vào sự xuất sắc giáo dục.',
      en: "Organize employee profiles, track schedules, and seamlessly manage salaries. Mam Choi La's integrated tool helps you maintain an efficient staff team, focusing on excellence in education.",
    },
    image: 'teacher-staff-management.png',
  },
  {
    title: {
      vi: '08. Cấu hình mẫu Lương',
      en: '08. Salary Template Configuration',
    },
    subTitle: {
      vi: 'Tuỳ Chỉnh Cách Tính Lương Theo Nhu Cầu',
      en: 'Customize Salary Calculation to Suit Needs',
    },
    description: {
      vi: 'Cá nhân hóa mẫu phiếu lương cho phù hợp với nhu cầu của trường bạn. Chỉ cần thiết lập mẫu một lần, mọi tính toán, khấu trừ, và thưởng sẽ được tự động hóa, đảm bảo mỗi bảng lương của nhân viên đều được xử lý chính xác và kịp thời.',
      en: "Personalize salary slips to fit your school's requirements. Simply set up the template once, and all calculations, deductions, and bonuses will be automated, ensuring each employee's payroll is handled accurately and on time.",
    },
    image: 'salary-template-configuration.png',
  },
  {
    title: {
      vi: '09. Quản lý Lương tự động',
      en: '09. Automatic Salary Management',
    },
    subTitle: {
      vi: 'Quy Trình Tính Lương Tự Động, Chính Xác',
      en: 'Accurate Automated Payroll Process',
    },
    description: {
      vi: 'Tự động hóa các tính toán và phân phối tiền lương một cách chính xác. Đảm bảo nhân viên được trả đúng và đủ, đúng hạn, chỉ với cú chạm từ phía bạn.',
      en: 'Automate calculations and accurately distribute salaries. Ensure employees are paid correctly and on time with just a touch from your end.',
    },
    image: 'automatic-salary-management.png',
  },
  {
    title: {
      vi: '10. Tin nhắn Zalo tự động',
      en: '10. Automatic Zalo Messaging',
    },
    subTitle: {
      vi: 'Thông Báo Tức Thì Qua Zalo',
      en: 'Instant Notifications via Zalo',
    },
    description: {
      vi: 'Giữ liên lạc với phụ huynh và nhân viên một cách nhanh chóng và dễ dàng với thông báo tự động qua Zalo. Từ thông báo điểm danh, ra về đến nhắc nhở học phí, sự kiện, việc liên lạc chưa bao giờ đơn giản đến thế.',
      en: 'Stay in touch with parents and staff quickly and easily with automatic notifications via Zalo. From attendance notifications, departure reminders to fee reminders, events, communication has never been simpler.',
    },
    image: 'automatic-zalo-messaging.png',
    subFeatures: [
      {
        vi: 'Tin nhắn tuyển sinh',
        en: 'Admissions Messaging',
      },
      {
        vi: 'Tin nhắn điểm danh',
        en: 'Attendance Messaging',
      },
      {
        vi: 'Tin nhắn học phí',
        en: 'Tuition Fee Messaging',
      },
    ],
  },
  {
    title: {
      vi: '11. Cổng thông tin trực tuyến cho Phụ huynh',
      en: '11. Online Portal for Parents',
    },
    subTitle: {
      vi: 'Kết Nối Phụ Huynh Với Cổng Thông Tin Số',
      en: 'Connect Parents with Digital Information Hub',
    },
    description: {
      vi: 'Thu hẹp khoảng cách thông tin với Cổng Thông Tin Phụ Huynh Số của Mầm Chồi Lá. Cung cấp cho phụ huynh quyền truy cập tức thì vào tiến trình học tập, các sự kiện sắp tới và hoạt động, thực đơn hằng ngày của con em họ. Đây là trạm dừng một chỗ cho sự tham gia của phụ huynh, giúp Phụ huynh luôn được cập nhật và gắn kết với hành trình giáo dục của con.',
      en: "Bridge the information gap with Mam Choi La's Digital Parent Information Portal. Provide parents instant access to their children's learning progress, upcoming events and activities, and daily menus. It's a one-stop station for parental engagement, keeping parents updated and connected with their child's educational journey.",
    },
    image: 'online-portal-parents.png',
  },
  {
    title: {
      vi: '12.Báo cáo Tài chính',
      en: '12. Financial Report',
    },
    subTitle: {
      vi: 'Báo Cáo Tài Chính Tự Động, Rõ Ràng',
      en: 'Automated, Clear Financial Reporting',
    },
    description: {
      vi: 'Ra quyết định thông minh & kịp thời với báo cáo tài chính tự động. Hệ thống của chúng tôi tạo ra các báo cáo chi tiết, cho bạn cái nhìn tổng quan về tình hình tài chính chỉ trong một cái nhìn.',
      en: 'Make smart and timely decisions with automated financial reports. Our system generates detailed reports, giving you an overview of the financial situation at a glance.',
    },
    image: 'financial-report.png',
  },
];

const SYSTEM_SECTION = [
  {
    subTitle: 'leading',
    title: 'technologies',
    description: 'technologiesDescription',
    actions: [
      {
        icon: 'monitor-recorder',
        text: 'iOTAIDevicesIntegration',
      },
      {
        icon: 'cpu-charge',
        text: 'latestTechApplied',
      },
    ],
  },
  {
    subTitle: 'extreme',
    title: 'conveniences',
    description: 'conveniencesDescription',
    actions: [
      {
        icon: 'airdrop',
        text: 'maximizedAutomation',
      },
      {
        icon: 'mobile',
        text: 'mobilePortals',
      },
    ],
  },
  {
    subTitle: 'advanced',
    title: 'digitals',
    description: 'digitalsDescription',
    actions: [
      {
        icon: 'security-time',
        text: 'transparentHistory',
      },
      {
        icon: 'status-up',
        text: 'visualizedReports',
      },
    ],
  },
];

const Home = ({ meta }: any) => {
  const router = useRouter();
  const locale = useRouter().locale as keyof ILanguageInput;
  const trans: any = useTrans();
  const sliderRef = useRef<any>(null);
  const introduceRef = useRef<any>(null);
  const featuresRef = useRef<any>(null);
  const [slideIndex, setSlideIndex] = useState(0);
  const { headerItem, setHeaderItem } = useContext<any>(AppContext);

  const settings = {
    centerMode: true,
    infinite: true,
    slidesToShow: 3,
    vertical: true,
    slidesToScroll: 1,
    speed: 1000,
    autoplay: true,
    className: 'center',
    centerPadding: '0',
    arrows: false,
    pauseOnHover: false,
  };

  const mobileSettings = {
    ...settings,
    slidesToShow: 1,
    vertical: false,
    autoplay: false,
  };

  const mouseEvent = useCallback((hover: boolean) => {
    return hover
      ? sliderRef?.current?.slickPause()
      : sliderRef?.current?.slickPlay();
  }, []);

  useEffect(() => {
    const anchor = 50;
    if (!!headerItem && !['introduce', 'features'].includes(headerItem)) {
      handleScrollingTo(
        (document.getElementById(headerItem)?.offsetTop || 0) - anchor,
      );
    } else {
      if (headerItem === 'introduce') {
        handleScrollingTo((introduceRef?.current.offsetTop || 0) - anchor);
      }
      if (headerItem === 'features') {
        handleScrollingTo((featuresRef?.current.offsetTop || 0) - anchor);
      }
    }
    setTimeout(() => {
      setHeaderItem('');
    }, 0);
  }, [headerItem, setHeaderItem]);

  useEffect(() => {
    const language = localStorage.getItem('language');
    router.push(
      {
        pathname: router.pathname,
        query: router.query,
      },
      {
        pathname: router.pathname,
        query: router.query,
      },
      { locale: language || 'vi' },
    );
  }, []);

  const opts = {
    height: '400',
    width: '100%',
    playerVars: {
      // https://developers.google.com/youtube/player_parameters
      autoplay: 0,
    },
  };

  return (
    <Main meta={<Meta {...meta} />}>
      <section className={`max-w-[1440px] pt-0 ${sectionClassName}`}>
        <div className="relative grid grid-cols-[60%_auto] gap-x-10 lg:grid-cols-1">
          <div className={`py-8 pl-24 lg:py-4 lg:pl-0`}>
            <div className={`mb-10 flex flex-col gap-y-4 md:mb-8`}>
              <h5 className={`barlow text-18 text-red underline md:text-14`}>
                <b>{trans.homePage.introductionSection.title}</b>
              </h5>
              <div
                className={`my-[-88px] flex items-center space-x-4 md:-my-12`}
              >
                <p
                  className={'whitespace-nowrap text-72 lg:text-48 md:text-40'}
                >
                  {trans.homePage.introductionSection.better}{' '}
                </p>
                {/* @ts-ignore */}
                <Slider
                  {...settings}
                  ref={sliderRef}
                  afterChange={(index) => setSlideIndex(index)}
                >
                  {[...SLIDER, ...SLIDER].map((item, index) => (
                    <div key={index}>
                      <h1
                        className={`bg-primaryGradient bg-clip-text text-72 text-transparent md:text-40`}
                      >
                        {item.title(locale)}.
                      </h1>
                    </div>
                  ))}
                </Slider>
              </div>
              <p className={`barlow max-w-[80%] md:max-w-full`}>
                {trans.homePage.introductionSection.description}
              </p>
            </div>
            <div className={`flex items-center gap-x-8 md:hidden`}>
              <Button url={'/get-quote'}>{trans.requestADemo}</Button>
              <AppLink
                href={SLIDER[slideIndex]?.card.url || '/'}
                text={trans.exploreMore}
              />
            </div>
            {/* App Direction Component */}
            <div
              className={`mt-10 flex items-start gap-x-6 md:hidden`}
              onMouseEnter={() => mouseEvent(true)}
              onMouseLeave={() => mouseEvent(false)}
            >
              {SLIDER.map((item, index) => {
                const isActive =
                  SLIDER[slideIndex > 2 ? slideIndex - 3 : slideIndex]
                    ?.title === item.title;
                return (
                  <div
                    key={index}
                    onMouseEnter={() => {
                      sliderRef?.current?.slickGoTo(index);
                      setSlideIndex(index);
                    }}
                    className={`
                      group/item flex cursor-pointer flex-col items-center justify-center gap-y-4 rounded-2xl bg-light px-6 py-10 shadow-normal transition-all duration-500 hover:bg-primaryGradient
                      ${isActive ? 'bg-primaryGradient' : ''}
                    `}
                  >
                    <AppImage src={`/assets/images/${item.card.image}`} />
                    <b
                      className={`
                        w-4/5 text-center text-natreul transition-all group-hover/item:bg-secondaryGradient group-hover/item:bg-clip-text group-hover/item:text-transparent
                        ${
                          isActive
                            ? 'bg-secondaryGradient bg-clip-text text-transparent'
                            : ''
                        }`}
                    >
                      {trans[item.card.text]}
                    </b>
                    <AppLink
                      iconHidden
                      type={'light'}
                      text={trans.moreDetail}
                      href={item.card.url || '/'}
                      className={'hidden !text-14 group-hover/item:block'}
                    />
                  </div>
                );
              })}
            </div>
            <div className={'hidden md:block'}>
              {/* @ts-ignore */}
              <Slider {...mobileSettings}>
                {SLIDER.map((item, index) => (
                  <div key={index}>
                    <div
                      className={`flex cursor-pointer flex-col items-center justify-center gap-y-4 rounded-2xl bg-light bg-primaryGradient px-6 py-10 transition-all duration-500`}
                    >
                      <AppImage src={`/assets/images/${item.card.image}`} />
                      <b
                        className={`w-4/5 bg-secondaryGradient bg-clip-text text-center text-transparent transition-all`}
                      >
                        {trans[item.card.text]}
                      </b>
                      <AppLink
                        iconHidden
                        type={'light'}
                        text={trans.moreDetail}
                        href={item.card.url || '/'}
                        className={'!text-14'}
                      />
                    </div>
                  </div>
                ))}
              </Slider>
            </div>
            <div className={`mt-8 hidden items-center gap-x-8 md:flex`}>
              <Button url={'/get-quote'}>{trans.requestADemo}</Button>
              <AppLink
                href={SLIDER[slideIndex]?.card.url || '/'}
                text={trans.exploreMore}
              />
            </div>
          </div>
          {/* Banner Images */}
          <div
            className={'pt-14 lg:hidden'}
            onMouseEnter={() => mouseEvent(true)}
            onMouseLeave={() => mouseEvent(false)}
          >
            <div className={'aspect-[1.34]'}>
              <AppImage
                src={`/assets/images/${
                  SLIDER[slideIndex > 2 ? slideIndex - 3 : slideIndex]?.image ||
                  ''
                }`}
              />
            </div>
          </div>
        </div>
      </section>
      {/* End of App Intro Section */}

      {/* Highlight Feature Section */}
      <section className={sectionClassName} ref={introduceRef}>
        <div
          className={`barlow mx-auto mb-10 max-w-[772px] space-y-4 text-center md:text-left`}
        >
          <h6 className={`text-18 text-red underline`}>
            <b>{trans.homePage.featureSection.title}</b>
          </h6>
          <p>{trans.homePage.featureSection.description}</p>
        </div>
        {/* Highlight Feature Listing */}
        <ul
          className={`mt-6 flex justify-between gap-x-6 md:flex-col md:gap-y-8`}
        >
          {FEATURES.map((item, index) => (
            <li
              key={index}
              className={`flex max-w-[260px] flex-col items-center gap-y-6 text-center md:max-w-full md:flex-row md:gap-x-4 md:text-left`}
            >
              <Icon name={item.icon} className={'fill-blue'} size={72} />
              <div>
                <p className={`text-24 text-green md:text-18`}>
                  {item.title?.[locale]()}
                </p>
                <p className={'barlow'}>{item.description?.[locale]()}</p>
              </div>
            </li>
          ))}
        </ul>

        <div
          className={`mt-16 flex flex-col items-center gap-y-14 md:items-start md:gap-y-6`}
        >
          <div
            className={
              'mx-auto h-[400px] w-full max-w-[770px] overflow-hidden rounded-2xl md:h-[220px]'
            }
          >
            {/* <ReactPlayer */}
            {/*  url="https://www.youtube.com/watch?v=WCZvsLAv4B0" */}
            {/*  width={'100%'} */}
            {/*  height={'100%'} */}
            {/* /> */}
            <YouTube videoId="WCZvsLAv4B0" opts={opts} />
          </div>

          <AppLink href={'/principals-management'} text={trans.moreDetail} />
        </div>
      </section>

      <section className={`${sectionClassName} md:px-0`} ref={featuresRef}>
        <h6
          className={`barlow text-center text-18 text-red underline md:px-4 md:text-left`}
        >
          <b>{trans.outstandingFeatures}</b>
        </h6>

        <div className={'mt-20 space-y-20 md:px-4'}>
          {OUTSTANDING_FEATURES.map((item: any, index: number) => {
            const isOdd = index % 2 !== 0;
            return (
              <div
                id={`feature-${index + 1}`}
                key={index}
                className={classNames(
                  `grid items-center gap-x-[min(10%,84px)] md:grid-cols-1 md:gap-y-2`,
                  {
                    'grid-cols-[auto_640px] lg:grid-cols-2': isOdd,
                    'grid-cols-[640px_auto] lg:grid-cols-2': !isOdd,
                  },
                )}
              >
                <div
                  className={`flex flex-col gap-y-4 md:gap-y-2 ${
                    isOdd
                      ? 'order-last items-start text-start'
                      : 'items-end text-end md:order-last md:items-start md:text-start'
                  }`}
                >
                  <h2 className={'barlow text-48 text-green md:text-24'}>
                    {item.title[locale]}
                  </h2>
                  <h4 className={'text-24 text-blue md:text-18'}>
                    <b>{item.subTitle[locale]}</b>
                  </h4>
                  <p className={'barlow'}>{item.description[locale]}</p>
                </div>
                <div>
                  <AppImage
                    src={`/assets/images/outstanding-features/${item.image}`}
                  />
                </div>
              </div>
            );
          })}
        </div>
      </section>

      <section className={sectionClassName}>
        <h6
          className={`barlow text-center text-18 text-red underline md:text-left`}
        >
          <b>{trans.weBringMoreBenefitsForYou}</b>
        </h6>
        <div className={'mt-16 flex flex-col gap-y-8 md:mt-8'}>
          {SYSTEM_SECTION.map((item, index) => {
            let className = '';
            if (index === 1) {
              className = 'mx-auto';
            } else if (index === 2) {
              className = 'self-end';
            }
            return (
              <div
                className={`flex w-[84%] gap-x-8 ${className} lg:w-full md:flex-col md:gap-y-4`}
                key={index}
              >
                <div
                  className={
                    'flex-1 space-y-6 rounded-2xl bg-[rgba(218,221,231,0.30)] p-12 lg:space-y-2 lg:p-4 md:order-last'
                  }
                >
                  <p className={'text-24 lg:text-16 md:text-14'}>
                    {trans[item.subTitle]}
                  </p>
                  <h2 className={'text-48 text-blue lg:text-24'}>
                    {trans[item.title]}
                  </h2>
                  <p>{trans.homePage[item.description]}</p>
                </div>
                <div className={'flex gap-x-8 lg:gap-y-4 md:gap-x-4'}>
                  {item.actions.map((action, idx) => (
                    <div
                      key={idx}
                      className={
                        'group flex w-[184px] cursor-pointer flex-col items-center justify-center gap-y-8 rounded-2xl bg-white px-8 hover:bg-primaryGradient lg:gap-y-4 lg:p-4 md:w-[50%] md:flex-row md:gap-x-4'
                      }
                    >
                      <Icon
                        size={null}
                        name={action.icon}
                        className={
                          'width-[110px] fill-dark group-hover:fill-white lg:h-12 lg:!w-12 md:!size-8'
                        }
                      />
                      <p
                        className={
                          'text-center group-hover:text-white md:flex-1 md:text-left'
                        }
                      >
                        <b>{trans[action.text]}</b>
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </section>

      {/* Customer Support Section */}
      <CustomerSupport />

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export async function getServerSideProps(context: any) {
  const { req, resolvedUrl, locale } = context;
  try {
    const seoWebsitesRef = await getSeoWebsites({
      clientdomain: req.headers.host,
    });
    const seoWebsitesRefData = await seoWebsitesRef.json();
    const seoData =
      seoWebsitesRefData.data?.find((item: any) => item.page === resolvedUrl) ||
      {};
    return {
      props: {
        meta: formatMeta(seoData, locale),
      },
    };
  } catch (error) {
    console.log(getErrorMessage(error));
  }
  return {
    props: {},
  };
}

export default Home;

export const runtime = 'experimental-edge';
