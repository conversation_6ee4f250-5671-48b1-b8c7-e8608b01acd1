'use client';

import { useRouter } from 'next/router';
import React from 'react';

import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import TextGradient from '@/components/TextGradient';
import Title from '@/components/Title';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const HEADER = {
  subTitle: {
    en: 'Online enrollment',
    vi: 'Tuyển sinh trực tuyến',
  },
  title: {
    en: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Enroll'} className={'inline'} />{' '}
        {`more families with digital forms.`}
      </h1>
    ),
    vi: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Tuyển sinh'} className={'inline'} />{' '}
        {`nhiều học sinh hơn với các nền tảng trực tuyến.`}
      </h1>
    ),
  },
  description: {
    en: "Don't waste time on enrollment paperwork. Digital forms make enrollment convenient for your staff and families.",
    vi: 'Đừng lãng phí thời gian vào thủ tục đăng ký giấy tờ thủ công. Các biểu mẫu kỹ thuật số giúp việc đăng ký trở nên thuận tiện cho nhân viên, phụ huynh và trung tâm của bạn.',
  },
};

const HIGHLIGHTS = [
  {
    title: {
      en: 'Register families online',
      vi: 'Phụ huynh đăng ký trực tuyến',
    },
    description: {
      en: 'LineLeader by ChildcareCRM scoured hundreds of childcare enrollment packets to create an industry-standard packet—available digitally. Automatically collect family information, emergency contacts, consent and agreements, medical background, and all necessary state forms.',
      vi: 'Mầm Chồi Lá đã tìm hiểu và hợp tác với hàng trăm trung tâm giáo dục mầm non để tạo ra biểu mẫu trực tuyến chuẩn. Tự động thu thập thông tin gia đình, địa chỉ liên hệ khẩn cấp, sự đồng ý và thỏa thuận, lý lịch và hồ sơ học sinh, y tế và tất cả các thông tin cần thiết khi đăng ký tuyển sinh.',
    },
    images: ['submit_admission.jpg', 'adminssion_submit.jpg'],
  },
  {
    title: {
      en: 'Ensure enrollment forms are complete, every time',
      vi: 'Đảm bảo đơn đăng ký được hoàn tất nhanh chóng và dễ dàng',
    },
    description: {
      en: 'Eliminate chasing down missing paperwork or information. Use required fields to capture all necessary information.',
      vi: 'Loại bỏ việc theo đuổi các giấy tờ hoặc thất thoát thông tin bằng các bản lưu trữ kỹ thuật số. Sử dụng các trường bắt buộc để nắm bắt tất cả thông tin cần thiết.',
    },
    images: ['online_form.jpeg', 'teacher_consulting.jpg'],
  },
  {
    title: {
      en: 'Save time with pre-filled family information',
      vi: 'Tiết kiệm thời gian cho phụ huynh với một số thông tin chung đã được điền sẵn theo mẫu',
    },
    description: {
      en: `With paper enrollment packets, families sometimes have to reenter their information up to 9 times. Minimize the hassle with pre-filled form information - saved and populated from a family's record in LineLeader. Families save countless hours.`,
      vi: 'Với bộ hồ sơ đăng ký bằng giấy, có khi phụ huynh phải nhập lại thông tin có sẵn nhiều lần. Vì vậy Mầm Chồi Lá giảm thiểu rắc rối với thông tin biểu mẫu được điền sẵn. Các phụ huynh và trung tâm tiết kiệm được vô số giờ quý báu của mình.',
    },
    images: ['parent_smiling.jpeg', 'profile_detail.jpg'],
  },
  {
    title: {
      en: 'Automate reminders to families',
      vi: 'Tự động nhắc nhở và cập nhật tình trạng tuyển sinh cho phụ huynh',
    },
    description: {
      en: 'Automatically send an email with your enrollment packet link. Then, support families with text and email reminders as they fill out enrollment forms.',
      vi: 'Tự động gửi email kèm theo liên kết hồ sơ tuyển sinh đã đăng lý. Sau đó, hỗ trợ nhắc nhở gia đình bằng tin nhắn và email khi họ điền vào mẫu đăng ký.',
    },
    images: ['email_submited.jpg', 'email_accepted.jpg'],
  },
];

const QUOTE = {
  en: 'Stop chasing down families to collect paperwork',
  vi: 'Dừng lại việc thu thập giấy tờ thủ công từ các phụ huynh',
};

const BENEFITS = {
  title: {
    en: 'Benefits of using Digital Admission Forms',
    vi: 'Lợi ích của việc sử dụng Nền tảng tuyển sinh trực tuyến',
  },
  data: [
    {
      icon: 'lovely',
      text: {
        en: 'Save time',
        vi: 'Tiết kiệm thời gian hơn',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Simplify registration',
        vi: 'Đăng ký một cách dễ dàng',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Increase form completions',
        vi: 'Đẩy nhanh quá trình hoàn tất hồ sơ tuyển sinh',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Be inclusive',
        vi: 'Tăng mức độ phát triển công nghệ của trung tâm',
      },
    },
  ],
};

const OnlineEnrollment = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <Main meta={<Meta title={trans.onlineEnrollment} description={''} />}>
      <section className={`${sectionClassName} pt-0`}>
        <div className={'grid grid-cols-2 lg:grid-cols-1 lg:gap-y-2'}>
          <div className={'space-y-10 md:space-y-6'}>
            <h5 className={`barlow mb-10 text-18 text-red underline`}>
              <b>{HEADER.subTitle[locale]}</b>
            </h5>
            {HEADER.title[locale]()}
            <p className={'mb-14 mt-10 max-w-[506px]'}>
              {HEADER.description[locale]}
            </p>
            <div
              className={'flex items-center gap-8 lg:flex-col lg:items-start'}
            >
              <AppLink href={'/get-quote'} text={trans.requestADemo} />
              <AppLink
                href={'/principals-management'}
                text={trans.seeItInAction}
              />
            </div>
          </div>
          <AppImage
            src="/assets/images/online-enrollment/header.png"
            className={'!object-contain lg:mx-auto lg:max-w-[480px] md:hidden'}
          />
        </div>
      </section>

      <section className={sectionClassName}>
        <div className={'space-y-[112px] md:space-y-12'}>
          {HIGHLIGHTS.map((box, index) => (
            <div key={index} className={'space-y-12 md:space-y-6'}>
              <div
                className={'max-w-[70%] space-y-6 lg:max-w-full md:space-y-4'}
              >
                <Title text={box.title[locale]} />
                <p className={'barlow'}>{box.description[locale]}</p>
              </div>
              <div className={'grid grid-cols-2 gap-x-6 md:gap-x-4'}>
                {box.images.map((img, idx) => (
                  <div key={idx} className={'aspect-[640/400]'}>
                    <AppImage
                      src={`/assets/images/online-enrollment/${img}`}
                      className={'rounded-lg'}
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      <section className={`${sectionClassName} max-w-full bg-blue`}>
        <h2
          className={
            'barlow--bold mx-auto max-w-[1048px] px-4 text-center text-48 text-yellow  lg:text-32 md:py-3 md:text-24'
          }
        >
          {QUOTE[locale]}
        </h2>
      </section>

      <section className={`${sectionClassName} max-w-[1600px]`}>
        <h5
          className={`barlow mb-16 text-center text-18 text-red underline md:mb-6 md:text-left`}
        >
          <b>{BENEFITS.title[locale]}</b>
        </h5>
        <div
          className={
            'grid grid-cols-4 gap-6 lg:grid-cols-2 md:grid-cols-1 md:gap-y-4 md:px-0'
          }
        >
          {BENEFITS.data.map((item, index) => (
            <div
              key={index}
              className={
                'flex flex-col items-center gap-y-6 rounded-2xl bg-white p-12 text-center shadow-normal md:flex-row md:gap-x-4 md:p-6 md:text-left'
              }
            >
              <div
                className={
                  'flex h-12 w-12 items-center justify-center rounded-full bg-blue/10'
                }
              >
                <Icon name={item.icon} className={'fill-blue'} />
              </div>
              <p>
                <b>{item.text[locale]}</b>
              </p>
            </div>
          ))}
        </div>
      </section>

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export default OnlineEnrollment;
