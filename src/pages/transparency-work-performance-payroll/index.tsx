'use client';

import { useRouter } from 'next/router';
import React from 'react';

import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Button from '@/components/Button';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import TextGradient from '@/components/TextGradient';
import Title from '@/components/Title';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const HEADER = {
  subTitle: {
    en: 'Work performance & payroll',
    vi: 'Hiệu suất công việc & Lương thưởng',
  },
  title: {
    en: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Automate'} className={'inline'} />{' '}
        {`payments and financial reporting.`}
      </h1>
    ),
    vi: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Tự động hoá'} className={'inline'} />{' '}
        {`bảng lương và báo cáo công việc.`}
      </h1>
    ),
  },
  description: {
    en: 'Manage performance at-a-glance. Monitor staff productivity, enrollment pipeline health, and campaign sources. Plan ahead, track attendance, oversee engagement, and get nuanced insight. Save time with automated reports.',
    vi: 'Đánh giá hiệu suất cá nhân hiệu quả cho nhân viên. Giúp nhân viên tự đánh giá hiệu suất và sự phát triển chuyên nghiệp của họ. Nhân viên có thể tham gia vào quá trình tự cải tiến liên tục bằng cách theo dõi hoạt động hàng ngày của họ.',
  },
};

const HIGHLIGHTS = [
  {
    title: {
      en: 'Get paid faster, every time',
      vi: 'Cập nhật và xác nhận bảng chấm công nhanh chóng',
    },
    description: {
      en: 'Stop wasting valuable time by automatically collecting tuition and fees. Automate fee reminders to parents or never miss a payment with autopay.',
      vi: 'Stop wasting valuable time by automatically collecting tuition and fees. Automate fee reminders to parents or never miss a payment with autopay.',
    },
    listing: {
      en: () => (
        <>
          <span className={'barlow--bold'}>
            Automate Payroll and Invoicing.
          </span>{' '}
          Offer a convenient option for parents to pay when they want, how they
          want. Easily send, store, and organize invoices in one place.
        </>
      ),
      vi: () => (
        <>
          <span className={'barlow--bold'}>
            Tự động tính lương và lập phiếu lương.
          </span>{' '}
          Cung cấp một lựa chọn thuận tiện để nhân viên nhận lương khi họ muốn,
          theo cách họ muốn. Dễ dàng gửi, lưu trữ và sắp xếp phiếu lương cùng
          một nơi.
        </>
      ),
    },
    images: ['working_calendar.jpg', 'leave_history.jpg'],
  },
  {
    title: {
      en: 'Manage subsidies and invoices-hands-free',
      vi: 'Quản lý hỗ trợ tài chính và hóa đơn - hoàn toàn tự động.',
    },
    description: {
      en: 'Effortlessly grant subsidy funds to families. Manage invoices, establish recurring plans, and track finances. ',
      vi: 'Dễ dàng trợ cấp lương thưởng cho nhân viên. Quản lý hóa đơn, thiết lập kế hoạch định kỳ và theo dõi tài chính.',
    },
    listing: {
      en: () => (
        <>
          <span className={'barlow--bold'}>Subsidy Management.</span> Manage
          invoices, establish recurring plans, and track finances. Secure and
          distribute proper funding to avoid decreased profits and enrollment
          gaps. Plus, multi-center childcare businesses can oversee multiple
          subsidies with ease. Maintain compliance with subsidy reporting.
        </>
      ),
      vi: () => (
        <>
          <span className={'barlow--bold'}>Quản lý trợ cấp.</span> Manage
          invoices, establish recurring plans, and track finances. Secure and
          distribute proper funding to avoid decreased profits and enrollment
          gaps. Plus, multi-center childcare businesses can oversee multiple
          subsidies with ease. Maintain compliance with subsidy reporting.
        </>
      ),
    },
    images: ['payroll.jpg', 'pay_slip.jpg'],
  },
  {
    title: {
      en: 'Report on revenue with ease',
      vi: 'Báo cáo tài chính dễ dàng',
    },
    description: {
      en: `Effortlessly forecast finances for each center. Project enrollment ratios, trends, and annual profit for the upcoming year. `,
      vi: 'Effortlessly forecast finances for each center. Project enrollment ratios, trends, and annual profit for the upcoming year. ',
    },
    listing: {
      en: () => (
        <>
          <span className={'barlow--bold'}>One-Click Revenue Reporting.</span>{' '}
          Track and report on revenue. Estimate your annual profit to forecast
          finances for the upcoming school year. Gain the insight you need to
          deliver an outstanding family experience.
        </>
      ),
      vi: () => (
        <>
          <span className={'barlow--bold'}>
            Báo cáo tài chính chỉ với 1 chạm.
          </span>{' '}
          Theo dõi và báo cáo lương thưởng. Thống kê chi tiết theo tháng và năm
          về các loại thu nhập lương, thưởng và trợ cấp riêng biệt. Để Giáo viên
          có thể có kế hoạch với tài chính sắp tới của họ.
        </>
      ),
    },
    images: ['performance_report.jpg', 'attendance_history.jpg'],
  },
];

const QUOTE = {
  en: 'Automate financial reporting and payroll, in a few clicks',
  vi: 'Tự động hóa báo cáo tài chính và tính lương chỉ trong một chạm',
};

const TransparencyWorkPerformancePayroll = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <Main
      meta={
        <Meta
          title={trans.transparencyWorkPerformancePayroll}
          description={''}
        />
      }
    >
      <section className={`${sectionClassName} pt-0`}>
        <div className={'grid grid-cols-[64%_auto] lg:grid-cols-1 lg:gap-y-2'}>
          <div className={'space-y-10 md:space-y-6'}>
            <h5 className={`barlow mb-10 text-18 text-red underline`}>
              <b>{HEADER.subTitle[locale]}</b>
            </h5>
            {HEADER.title[locale]()}
            <p className={'mb-14 mt-10 max-w-[506px]'}>
              {HEADER.description[locale]}
            </p>
            <div
              className={'flex items-center gap-8 lg:flex-col lg:items-start'}
            >
              <Button className={'!rounded-[30px] bg-white'}>
                <>
                  <span className={'text-dark'}>{trans.inviteYourSchool}</span>
                  <Icon name={'export-circle'} className={'fill-dark'} />
                </>
              </Button>
              <AppLink
                href={'/educators-application'}
                text={trans.seeItInAction}
              />
            </div>
          </div>
          <AppImage
            src="/assets/images/transparency-work-performance-payroll/header.png"
            className={'!object-contain lg:mx-auto lg:max-w-[480px] md:hidden'}
          />
        </div>
      </section>

      <section className={sectionClassName}>
        <div className={'space-y-[112px] md:space-y-12'}>
          {HIGHLIGHTS.map((box, index) => (
            <div key={index} className={'space-y-12 md:space-y-6'}>
              <div
                className={'max-w-[70%] space-y-6 lg:max-w-full md:space-y-4'}
              >
                <Title text={box.title[locale]} />
                <p className={'barlow'}>{box.description[locale]}</p>
                <p className={'barlow'}>{box.listing[locale]()}</p>
              </div>
              <div className={'grid grid-cols-2 gap-x-6 md:gap-x-4'}>
                {box.images.map((img, idx) => (
                  <div key={idx} className={'aspect-[640/400]'}>
                    <AppImage
                      src={`/assets/images/transparency-work-performance-payroll/${img}`}
                      className={'rounded-lg'}
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      <section className={`${sectionClassName} max-w-full bg-blue`}>
        <h2
          className={
            'barlow--bold mx-auto max-w-[1048px] px-4 text-center text-48 text-yellow  lg:text-32 md:py-3 md:text-24'
          }
        >
          {QUOTE[locale]}
        </h2>
      </section>

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export default TransparencyWorkPerformancePayroll;
