'use client';

import React from 'react';

import BlogFilter from '@/components/BlogFilter';
import CustomerSupport from '@/components/Customer';
import FaqListing from '@/components/Faq';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';

const Faq = () => {
  return (
    <Main meta={<Meta title={''} description={''} />}>
      <div
        className={`mx-auto flex h-full min-h-[820px] flex-col justify-between pt-[120px]`}
      >
        <div className={`mb-[64px] px-24 pt-[120px]`}>
          <div
            className={`mx-auto mb-[40px] flex h-full w-full max-w-[1728px] flex-col items-center justify-center px-24 text-center`}
          >
            <p
              className={`mb-[24px] bg-gradient-to-tr from-[#EE9CA7] to-[#FFDDE1] bg-clip-text text-[72px] font-bold text-transparent`}
            >
              Frequently
              <span className={`font-normal text-[#58595B]`}>
                {' '}
                asked questions.
              </span>
            </p>
          </div>
        </div>

        {/* Blog Search & Filter Section */}
        <BlogFilter />

        {/* FAQ */}
        <div
          className={`mx-auto mb-[168px] flex h-full w-full max-w-[1728px] flex-col items-center justify-between px-24`}
        >
          <FaqListing />
        </div>

        {/* Customer Support Section */}
        <CustomerSupport />
      </div>
    </Main>
  );
};

export default Faq;
