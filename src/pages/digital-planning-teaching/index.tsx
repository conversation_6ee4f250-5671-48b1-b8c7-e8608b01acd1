'use client';

import { useRouter } from 'next/router';
import React from 'react';

import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Button from '@/components/Button';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import TextGradient from '@/components/TextGradient';
import Title from '@/components/Title';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const HEADER = {
  subTitle: {
    en: 'Digital planning & teaching',
    vi: 'Giáo án kỹ thuật số & Dạy học',
  },
  title: {
    en: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Enhance'} className={'inline'} />{' '}
        {`curriculum quality with simple lesson planning.`}
      </h1>
    ),
    vi: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Nâng cao'} className={'inline'} />{' '}
        {`chất lượng giảng dạy bằng công cụ soạn giáo án trực tuyến.`}
      </h1>
    ),
  },
  description: {
    en: "Alleviate your educators' stress. Stop wasting time writing curriculum by hand. Equip educators with easy-to-use tools. Simplify lesson-building, track child progress, improve learning outcomes, and keep families in the loop.",
    vi: 'Giảm bớt công việc thủ công cho giáo viên. Ngừng lãng phí thời gian viết giáo án bằng tay. Trang bị cho giáo viên các công cụ dễ sử dụng. Đơn giản hóa việc xây dựng bài học, theo dõi tiến bộ của trẻ em, cải thiện kết quả học tập và giữ cho gia đình luôn tham gia vào quá trình giáo dục.',
  },
};

const HIGHLIGHTS = [
  {
    title: {
      en: 'Digitally plan curriculum',
      vi: 'Soạn giáo án điện tử',
    },
    description: {
      en: 'Let educators build a best-in-class curriculum with ease. Save time with digitized lesson planning, one-click access to planned activities, and real-time access to updates.',
      vi: 'Hãy để Giáo viên xây dựng một chương trình giảng dạy tốt nhất một cách dễ dàng. Tiết kiệm thời gian với việc lập giáo án bài học được số hóa, truy cập dễ dàng, lên kế hoạch và cập nhật theo nhanh chóng theo thời gian thực.',
    },
    images: ['curriculum_plan.jpeg', 'teacher_working.jpg'],
  },
  {
    title: {
      en: 'Improve educator collaboration',
      vi: 'Cải thiện sự hợp tác của Giáo viên',
    },
    description: {
      en: 'Simplify teacher collaboration through shared lesson planning. Corporate locations can set lesson plans and cascade them down to all schools, making it easy for educators to work together on a unified platform. Allow teachers to submit requests for curriculum updates without them having to build lesson plans from scratch. Plus, directors can access a planning calendar to ensure everyone stays on track.',
      vi: 'Đơn giản hóa sự cộng tác của Giáo viên thông qua việc soạn giáo án chung. Xoá bỏ rào cản văn phòng và cung cấp các công cụ trực tuyến giúp Giáo viên dễ dàng làm việc cùng nhau trên một nền tảng thống nhất. Cho phép Giáo viên gửi yêu cầu cập nhật chương trình giảng dạy mà không cần phải xây dựng giáo án từ đầu. Ngoài ra, Hiệu trưởng có thể truy cập lịch lập kế hoạch để đảm bảo mọi người luôn đi đúng hướng.',
    },
    images: ['teacher_guide.jpg', 'collaboration_work.jpeg'],
  },
  {
    title: {
      en: 'Monitor and share learning progress with parents',
      vi: 'Theo dõi và chia sẻ tiến độ học tập với phụ huynh',
    },
    description: {
      en: 'Identify low areas of your supply stock. Ensure parents always have the essentials, like diapers, bottles, wipes, and more.',
      vi: 'Identify low areas of your supply stock. Ensure parents always have the essentials, like diapers, bottles, wipes, and more.',
    },
    images: ['subject_detail.jpeg', 'give_badge.jpeg'],
  },
];

const QUOTE = {
  en: 'Save time and elevate program quality with curriculum management',
  vi: 'Tiết kiệm thời gian và nâng cao chất lượng giảng dạy với quản lý giáo án điện tử',
};

const BENEFITS = {
  title: {
    en: 'Benefits of using Online Curriculum Planning',
    vi: 'Lợi ích của việc sử dụng Xây dựng giáo án trực tuyến',
  },
  data: [
    {
      icon: 'lovely',
      text: {
        en: 'Save time with digital lesson planning',
        vi: 'Tiết kiệm thời gian với việc soạn giáo án điện tử',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Track learning progress with ease',
        vi: 'Theo dõi tiến độ học tập một cách dễ dàng',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Collaborate with ease',
        vi: 'Cộng tác một cách dễ dàng',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Keep parents in the loop',
        vi: 'Giúp phụ huynh cùng tham gia quá trình giảng dạy',
      },
    },
  ],
};

const DigitalPlanningTeaching = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <Main
      meta={<Meta title={trans.digitalPlanningTeaching} description={''} />}
    >
      <section className={`${sectionClassName} pt-0`}>
        <div className={'grid grid-cols-[64%_auto] lg:grid-cols-1 lg:gap-y-2'}>
          <div className={'space-y-10 md:space-y-6'}>
            <h5 className={`barlow mb-10 text-18 text-red underline`}>
              <b>{HEADER.subTitle[locale]}</b>
            </h5>
            {HEADER.title[locale]()}
            <p className={'mb-14 mt-10 max-w-[506px]'}>
              {HEADER.description[locale]}
            </p>
            <div
              className={'flex items-center gap-8 lg:flex-col lg:items-start'}
            >
              <Button className={'!rounded-[30px] bg-white'}>
                <>
                  <span className={'text-dark'}>{trans.inviteYourSchool}</span>
                  <Icon name={'export-circle'} className={'fill-dark'} />
                </>
              </Button>
              <AppLink
                href={'/educators-application'}
                text={trans.seeItInAction}
              />
            </div>
          </div>
          <AppImage
            src="/assets/images/digital-planning-teaching/header.png"
            className={'!object-contain lg:mx-auto lg:max-w-[480px] md:hidden'}
          />
        </div>
      </section>

      <section className={sectionClassName}>
        <div className={'space-y-[112px] md:space-y-12'}>
          {HIGHLIGHTS.map((box, index) => (
            <div key={index} className={'space-y-12 md:space-y-6'}>
              <div
                className={'max-w-[70%] space-y-6 lg:max-w-full md:space-y-4'}
              >
                <Title text={box.title[locale]} />
                <p className={'barlow'}>{box.description[locale]}</p>
              </div>
              <div className={'grid grid-cols-2 gap-x-6 md:gap-x-4'}>
                {box.images.map((img, idx) => (
                  <div key={idx} className={'aspect-[640/400]'}>
                    <AppImage
                      src={`/assets/images/digital-planning-teaching/${img}`}
                      className={'rounded-lg'}
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      <section className={`${sectionClassName} max-w-full bg-blue`}>
        <h2
          className={
            'barlow--bold mx-auto max-w-[1048px] px-4 text-center text-48 text-yellow  lg:text-32 md:py-3 md:text-24'
          }
        >
          {QUOTE[locale]}
        </h2>
      </section>

      <section className={`${sectionClassName} max-w-[1600px]`}>
        <h5
          className={`barlow mb-16 text-center text-18 text-red underline md:mb-6 md:text-left`}
        >
          <b>{BENEFITS.title[locale]}</b>
        </h5>
        <div
          className={
            'grid grid-cols-4 gap-6 lg:grid-cols-2 md:grid-cols-1 md:gap-y-4 md:px-0'
          }
        >
          {BENEFITS.data.map((item, index) => (
            <div
              key={index}
              className={
                'flex flex-col items-center gap-y-6 rounded-2xl bg-white p-12 text-center shadow-normal md:flex-row md:gap-x-4 md:p-6 md:text-left'
              }
            >
              <div
                className={
                  'flex h-12 w-12 items-center justify-center rounded-full bg-blue/10'
                }
              >
                <Icon name={item.icon} className={'fill-blue'} />
              </div>
              <p>
                <b>{item.text[locale]}</b>
              </p>
            </div>
          ))}
        </div>
      </section>

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export default DigitalPlanningTeaching;
