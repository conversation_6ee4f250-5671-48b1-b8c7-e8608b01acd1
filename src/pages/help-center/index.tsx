'use client';

import dayjs from 'dayjs';
import Link from 'next/link';
import { useRouter } from 'next/router';

import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Button from '@/components/Button';
import { CONTACT_SALES } from '@/components/Footer';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import TextGradient from '@/components/TextGradient';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const HELPS = [
  {
    icon: 'messages-3',
    title: {
      en: 'You have questions? please see our community FAQs',
      vi: 'Bạn có thắc mắc? Cùng xem câu hỏi thường gặp',
    },
    action: {
      text: {
        en: 'Our FAQ',
        vi: '<PERSON><PERSON><PERSON> hỏi thường gặp',
      },
      url: '/faq',
    },
  },
  {
    icon: 'book-2',
    title: {
      en: 'Our Guideline for to run your childcare center',
      vi: 'Hướng dẫn sử dụng hệ thống Mầm Chồi Lá cho trung tâm',
    },
    action: {
      text: {
        en: 'Our Guidelines',
        vi: 'Hướng dẫn sử dụng',
      },
      url: 'https://docs.google.com/presentation/d/1Rg9ah9pgsjDN2UPcwl9cwcrvloMoeTJWMFzPVu2ZdYs/edit?usp=sharing',
    },
  },
];

const Help = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;
  dayjs.locale(locale);

  return (
    <Main meta={<Meta title={trans.helpCenter} description={''} />}>
      <section className={`${sectionClassName} pt-0`}>
        <div
          className={
            'grid gap-x-[104px] lg:gap-y-6 min-lg:grid-cols-[550px_auto]'
          }
        >
          {locale === 'vi' ? (
            <div className={'space-y-10 md:space-y-4'}>
              <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
                {`Chúng tôi ở đây`} <br className={'lg:hidden'} /> để{' '}
                <TextGradient text={'giúp đỡ.'} className={'inline'} />
              </h1>
              <p className={'barlow'}>
                Đội ngũ chuyên gia hỗ trợ tận tâm của chúng tôi luôn sẵn sàng
                cung cấp các giải pháp kịp thời để giúp bạn khi đang sử dụng hệ
                thống Mầm Chồi Lá cho trung tâm của mình. Hãy truy cập Trung tâm
                hỗ trợ bên dưới để biết sản phẩm của bạn và có quyền truy cập 24
                giờ vào video, hướng dẫn cách thực hiện, hội thảo trực tuyến đào
                tạo miễn phí, Câu hỏi thường gặp và hơn thế nữa!
              </p>
            </div>
          ) : (
            <div className={'space-y-10 md:space-y-4'}>
              <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
                {`We're here`} <br className={'lg:hidden'} /> for{' '}
                <TextGradient text={'Help.'} className={'inline'} />
              </h1>
              <p className={'barlow'}>
                Our dedicated team of support professionals are available to
                provide timely solutions to help you with your Procure product.
                Visit the Support Center below for your product and have 24-hour
                access to videos, how-to guides, free training webinars, FAQs
                and more!
              </p>
            </div>
          )}
          <AppImage
            src="/assets/images/help_banner.jpg"
            className={'rounded-2xl md:aspect-[358/220] md:rounded-lg'}
          />
        </div>
      </section>

      {/* Help Item Section */}
      <section className={sectionClassName}>
        <div className={'grid gap-x-12 lg:gap-y-6 min-lg:grid-cols-3'}>
          {HELPS.map((item, index) => {
            const { title, action, icon } = item;
            return (
              <div
                key={index}
                className={
                  'space-y-16 rounded-2xl bg-white p-12 shadow-normal md:flex md:flex-col md:items-center md:space-y-2 md:p-6'
                }
              >
                <h3 className={'text-32 md:text-center md:text-16'}>
                  {title[locale]}
                </h3>
                <Icon
                  name={icon}
                  className={'h-[88px] w-[88px] md:h-16 md:w-16'}
                />
                <Button
                  url={action.url}
                  className={'!rounded-[30px] !bg-yellow md:w-full'}
                >
                  <>
                    <span className={'text-dark'}>{action.text[locale]}</span>
                    <Icon name={'export-circle'} className={'fill-dark'} />
                  </>
                </Button>
              </div>
            );
          })}
          {locale === 'vi' ? (
            <div className={'min-lg:p-12'}>
              <h3 className={'text-32 md:text-18'}>
                Hoặc bạn muốn được tư vấn kỹ hơn về hệ thống Mầm Chồi Lá!
              </h3>
              <h4 className={'mb-4 mt-6 text-24 text-blue md:text-16'}>
                Liên hệ tư vấn
              </h4>
              <ul className={`space-y-4`}>
                {CONTACT_SALES.map((link, index) => (
                  <li key={index} className={'space-y-2'}>
                    <p>{link.text}</p>
                    <Link href={`tel:${link.phone}`}>
                      <b>{link.phone}</b>
                    </Link>
                  </li>
                ))}
                <li>
                  <AppLink
                    href={`mailto:<EMAIL>`}
                    text={'<EMAIL>'}
                  />
                </li>
              </ul>
            </div>
          ) : (
            <div className={'min-lg:p-12'}>
              <h3 className={'text-32 md:text-18'}>
                And wanna sale support to start with us!
              </h3>
              <h4 className={'mb-4 mt-6 text-24 text-blue md:text-16'}>
                Contact Sales
              </h4>
              <ul className={`space-y-4`}>
                {CONTACT_SALES.map((link, index) => (
                  <li key={index} className={'space-y-2'}>
                    <p>{link.text}</p>
                    <Link href={`tel:${link.phone}`}>
                      <b>{link.phone}</b>
                    </Link>
                  </li>
                ))}
                <li>
                  <AppLink
                    href={`mailto:<EMAIL>`}
                    text={'<EMAIL>'}
                  />
                </li>
              </ul>
            </div>
          )}
        </div>
      </section>

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export default Help;
