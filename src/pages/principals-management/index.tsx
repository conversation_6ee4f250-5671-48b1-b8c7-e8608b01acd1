'use client';

import { useRouter } from 'next/router';
import React from 'react';
import ReactPlayer from 'react-player/lazy';

import AppHighlightSection from '@/components/AppHighlighSection';
import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Button from '@/components/Button';
import FaqListing from '@/components/Faq';
import FunctionsSection from '@/components/FunctionsSection';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import TextGradient from '@/components/TextGradient';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const HEADER = {
  title: {
    en: 'School operation system',
    vi: '<PERSON><PERSON> thống quản lý trung tâm gi<PERSON><PERSON> dục dành cho Hiệu trưởng',
  },
  description: {
    en: 'Comprehensive kindergarten operation management system designed to streamline administrative tasks and enhance overall efficiency in your educational institution.',
    vi: '<PERSON><PERSON> thống quản lý vận hành trường mẫu giáo toàn diện được thiết kế để tối ưu hóa các nhiệm vụ hành chính và nâng cao hiệu quả tổng thể trong trung tâm giáo dục của bạn.',
  },
  hey: {
    en: 'Good day, Principals!',
    vi: 'Xin chào, Hiệu trưởng!',
  },
  thisAppIsForYou: {
    en: 'this app is for you',
    vi: 'cùng tìm hiểu nhé.',
  },
};

const HIGHLIGHT = {
  title: {
    en: 'Principals/Directors love Mầm Chồi Lá',
    vi: 'Hiệu trưởng yêu thích Mầm Chồi Lá',
  },
  data: [
    {
      title: {
        en: 'Comprehensive Data Management.',
        vi: 'Quản lý dữ liệu toàn diện.',
      },
      description: {
        en: 'Our system offers robust data management capabilities, securely storing and organizing critical information such as student records, academic performance data, and administrative documents. Principals and directors can generate insightful reports, track progress, identify trends, and make data-driven decisions to drive continuous improvement and optimize educational outcomes.',
        vi: 'Mầm Chồi Lá cung cấp khả năng quản lý dữ liệu mạnh mẽ, lưu trữ và sắp xếp an toàn các thông tin quan trọng như hồ sơ học sinh, dữ liệu kết quả học tập và tài liệu hành chính. Hiệu trưởng và giám đốc có thể tạo các báo cáo chuyên sâu, theo dõi tiến độ, xác định xu hướng và đưa ra quyết định dựa trên dữ liệu để thúc đẩy cải tiến liên tục và tối ưu hóa kết quả giáo dục của trung tâm.',
      },
      image: '/assets/images/principal_love_mcl_1.jpg',
    },
    {
      title: {
        en: 'Professional Development Support.',
        vi: 'Hỗ trợ phát triển chuyên nghiệp.',
      },
      description: {
        en: 'Our system provides opportunities for professional development, offering access to educational resources, training materials, and networking opportunities. Principals and directors can support the growth and development of their educators, ensuring they stay updated with the latest teaching methodologies and best practices.',
        vi: 'Mầm Chồi Lá mang đến cơ hội phát triển nghề nghiệp, cung cấp quyền truy cập vào các tài nguyên giáo dục, tài liệu đào tạo và cơ hội kết nối mạng. Hiệu trưởng và giám đốc có thể hỗ trợ sự tăng trưởng và phát triển của các nhà giáo dục của họ, đảm bảo họ luôn cập nhật các phương pháp giảng dạy mới nhất và các phương pháp thực hành tốt nhất.',
      },
      image: '/assets/images/principal_love_mcl_2.jpg',
    },
    {
      title: {
        en: 'Enhanced Efficiency and Productivity.',
        vi: 'Nâng cao hiệu quả và năng suất.',
      },
      description: {
        en: 'By automating routine tasks and providing intuitive features, our system enhances overall efficiency and productivity. Principals and directors can focus on strategic initiatives, curriculum development, and fostering a positive learning environment.',
        vi: 'Bằng cách tự động hóa các tác vụ thường ngày và cung cấp các tính năng trực quan, Mầm Chồi Lá nâng cao hiệu quả và năng suất tổng thể. Hiệu trưởng và giám đốc có thể tập trung vào các sáng kiến chiến lược, phát triển chương trình giảng dạy và thúc đẩy môi trường học tập tích cực.',
      },
      image: '/assets/images/principal_love_mcl_3.jpg',
    },
  ],
};

const FUNCTIONS = {
  subTitle: {
    en: 'Feature that principals / directors could',
    vi: 'Mầm Chồi Lá cung cấp tính năng để hiệu trường / giám đốc có thể',
  },
  title: {
    en: 'Manage and run your childcare center',
    vi: 'Quản lý và vận hành trung tâm giáo dục tối ưu',
  },
  data: [
    {
      id: 1,
      icon: 'gallery',
      subTitle: {
        en: 'Enrollment Online & Management',
        vi: 'Quản lý & tuyển sinh trực tuyến',
      },
      title: {
        en: 'Powerful tool for managing and streamlining the enrollment process',
        vi: 'Công cụ mạnh mẽ để quản lý và tối ưu hóa quá trình tuyển sinh',
      },
      description: {
        en: "Principals can enhance the efficiency and accuracy of the enrollment process, ensuring a seamless experience for both students and parents. It's a modern approach to enrollment management in educational institutions.",
        vi: 'Hiệu trưởng có thể nâng cao hiệu quả và độ chính xác của quá trình tuyển sinh, đảm bảo trải nghiệm liền mạch cho cả học sinh và phụ huynh. Đó là một cách tiếp cận hiện đại để quản lý tuyển sinh trong các cơ sở giáo dục.',
      },
    },
    {
      id: 2,
      icon: 'trend-up',
      subTitle: {
        en: 'Kids & Parents Management',
        vi: 'Quản lý học sinh & phụ huynh',
      },
      title: {
        en: 'Ability to efficiently manage student profiles and the profiles of their parents and guardians',
        vi: 'Khả năng quản lý hiệu quả hồ sơ học sinh, phụ huynh và người giám hộ',
      },
      description: {
        en: "Principals can maintain accurate and up-to-date profiles for students and their families, fostering efficient communication and a well-organized educational environment. It's a modern solution for profile management in educational institutions.",
        vi: 'Hiệu trưởng có thể duy trì hồ sơ chính xác và cập nhật cho học sinh và gia đình, thúc đẩy giao tiếp hiệu quả và môi trường giáo dục được tổ chức tốt. Đó là một giải pháp hiện đại để quản lý hồ sơ trong các cơ sở giáo dục.',
      },
    },
    {
      id: 3,
      icon: 'gps',
      subTitle: {
        en: 'Staffs Management',
        vi: 'Quản lý nhân viên',
      },
      title: {
        en: 'Seamlessly manage all staff members, fostering a well-organized and efficient school environment',
        vi: 'Quản lý liền mạch tất cả nhân viên, thúc đẩy môi trường học tập được tổ chức tốt và hiệu quả',
      },
      description: {
        en: 'Principals can efficiently manage their staff, ensuring that all educators and support personnel contribute to a positive and productive school environment.',
        vi: 'Hiệu trưởng có thể quản lý đội ngũ nhân viên của mình một cách hiệu quả, đảm bảo rằng tất cả các giáo viên/bảo mẫu và nhân viên hỗ trợ đều đóng góp vào một môi trường học tập tích cực và hiệu quả.',
      },
    },
    {
      id: 4,
      icon: 'heart',
      subTitle: {
        en: 'Batch Messages',
        vi: 'Thông báo đồng loạt',
      },
      title: {
        en: 'Serves as a powerful communication hub',
        vi: 'Phục vụ như một trung tâm truyền thông mạnh mẽ',
      },
      description: {
        en: 'Principals can maintain open lines of communication, ensuring that parents, guardians, and staff members are always up to date with the latest news and announcements.',
        vi: 'Hiệu trưởng có thể duy trì đường dây liên lạc cởi mở, đảm bảo rằng phụ huynh, người giám hộ và nhân viên luôn cập nhật những tin tức và thông báo mới nhất.',
      },
    },
    {
      id: 5,
      icon: 'document-text',
      subTitle: {
        en: 'Events & Activities Management',
        vi: 'Quản lý sự kiện & hoạt động',
      },
      title: {
        en: 'Allowing principals to plan, coordinate, and oversee all activities quickly and effortlessly',
        vi: 'Cho phép hiệu trưởng lập kế hoạch, điều phối và giám sát mọi hoạt động một cách nhanh chóng và dễ dàng',
      },
      description: {
        en: "Principals can take charge of school events and activities, ensuring that they are well-organized, successful, and contribute to the school's overall mission.",
        vi: 'Hiệu trưởng có thể phụ trách các sự kiện và hoạt động của trường, đảm bảo rằng chúng được tổ chức tốt, thành công và đóng góp vào sứ mệnh chung của trường.',
      },
    },
    {
      id: 6,
      icon: 'security-user',
      subTitle: {
        en: 'Streamlined School Revenue',
        vi: 'Chuẩn xác hoá dòng tiền',
      },
      title: {
        en: 'Comprehensive School Finance Management for Principals',
        vi: 'Quản lý tài chính trường học toàn diện cho hiệu trưởng',
      },
      description: {
        en: 'Principals can maintain the financial health of the school, making it possible to invest in educational resources, infrastructure, and staff while ensuring financial stability.',
        vi: 'Hiệu trưởng có thể duy trì tình hình tài chính của trường, tạo điều kiện để đầu tư vào nguồn lực giáo dục, cơ sở hạ tầng và nhân viên trong khi vẫn đảm bảo sự ổn định tài chính.',
      },
    },
    {
      id: 7,
      icon: 'profile-2user',
      subTitle: {
        en: 'Visualized Dashboards',
        vi: 'Báo cáo trực quan',
      },
      title: {
        en: 'Comprehensive Oversight with a Visualized Dashboard',
        vi: 'Giám sát toàn diện với Bảng báo cáo trực quan',
      },
      description: {
        en: "Efficiently and effectively oversee all school operations, ensuring that the educational institution runs smoothly, and all stakeholders are informed and empowered. It's a modern solution for principals looking to lead with data and insights.",
        vi: 'Giám sát hiệu quả tất cả các hoạt động của trường, đảm bảo rằng cơ sở giáo dục hoạt động trơn tru và tất cả các bên liên quan đều được thông báo và trao quyền. Đó là một giải pháp hiện đại dành cho những hiệu trưởng muốn dẫn đầu bằng dữ liệu và thông tin chuyên sâu.',
      },
    },
    {
      id: 8,
      icon: 'card-pos',
      subTitle: {
        en: 'Receiving Payment Online',
        vi: 'Số hoá phương thức thanh toán',
      },
      title: {
        en: 'Ensuring transparency and accuracy in financial management',
        vi: 'Đảm bảo tính minh bạch, chính xác trong quản lý tài chính',
      },
      description: {
        en: "Track all financial transactions, guaranteeing sound financial management and the achievement of your school's financial objectives.",
        vi: 'Theo dõi tất cả các giao dịch tài chính, đảm bảo quản lý tài chính hợp lý và đạt được các mục tiêu tài chính trung tâm giáo dục bạn hướng tới.',
      },
    },
  ],
};

const QUOTE = {
  en: 'Cutting-edge technology, intuitive interfaces, and robust features to support your leadership role and optimize the management of your kindergarten',
  vi: 'Công nghệ tiên tiến, giao diện trực quan và các tính năng mạnh mẽ để hỗ trợ vai trò lãnh đạo và tối ưu hóa việc quản lý trung tâm giáo dục của bạn',
};

const PrincipalsManagement = () => {
  const locale = useRouter().locale as keyof ILanguageInput;
  const trans = useTrans();

  return (
    <Main meta={<Meta title={trans.principalsManagement} description={''} />}>
      <section className={sectionClassName}>
        <div
          className={
            'grid grid-cols-[60%_auto] gap-x-4 lg:grid-cols-1 lg:gap-y-2'
          }
        >
          <div className={'space-y-10 md:space-y-6'}>
            <h5 className={`barlow text-18 text-red underline md:text-14`}>
              <b>{HEADER.title[locale]}</b>
            </h5>
            <div>
              <TextGradient text={HEADER.hey[locale]} />
              <p className={'whitespace-nowrap text-72 lg:text-48 md:text-40'}>
                {HEADER.thisAppIsForYou[locale]}
              </p>
            </div>
            <p>{HEADER.description[locale]}</p>
            <div
              className={'flex items-center gap-8 lg:flex-col lg:items-start'}
            >
              <Button className={'!rounded-[30px] bg-white'}>
                <>
                  <span className={'text-dark'}>{trans.inviteYourSchool}</span>
                  <Icon name={'export-circle'} className={'fill-dark'} />
                </>
              </Button>
              <AppLink href={'/'} text={trans.seeItInAction} />
            </div>
          </div>
          <div>
            <AppImage
              src="/assets/images/principal-system-head-banner.png"
              className={'md:mt-8'}
            />
          </div>
        </div>
      </section>

      <AppHighlightSection
        title={HIGHLIGHT.title[locale]}
        data={HIGHLIGHT.data}
      />

      <section className={sectionClassName}>
        <div className={'flex justify-center'}>
          <div
            className={
              'mx-auto h-[400px] w-full max-w-[770px] overflow-hidden rounded-2xl md:aspect-[358/220] md:h-auto'
            }
          >
            <ReactPlayer
              url="https://www.youtube.com/watch?v=WCZvsLAv4B0"
              width={'100%'}
              height={'100%'}
            />
          </div>
        </div>
      </section>

      <FunctionsSection
        url={'principals-management'}
        subTitle={FUNCTIONS.subTitle[locale]}
        title={FUNCTIONS.title[locale]}
        data={FUNCTIONS.data}
      />

      <section className={`${sectionClassName} max-w-full bg-blue`}>
        <h2
          className={
            'barlow--bold mx-auto max-w-[1048px] px-4 text-center text-48 text-yellow  lg:text-32 md:py-3 md:text-24'
          }
        >
          {QUOTE[locale]}
        </h2>
      </section>

      {/* FAQ */}
      <FaqListing />

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export default PrincipalsManagement;
