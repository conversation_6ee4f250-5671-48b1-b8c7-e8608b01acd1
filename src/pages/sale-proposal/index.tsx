'use client';

import React, { useEffect, useRef, useState } from 'react';
import Slider from 'react-slick';

import AppImage from '@/components/AppImage';
import Button from '@/components/Button';
import Icon from '@/components/Icon';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { getSeoWebsites } from '@/services/api/seo-website';
import { formatMeta } from '@/utils';
import { getErrorMessage } from '@/utils/error';

const LANGUAGES = [
  {
    id: 'vi',
    icon: 'vi-flag.svg',
    name: 'Tiếng Việt',
  },
  {
    id: 'en',
    icon: 'en-flag.svg',
    name: 'English',
  },
];

const AWS_URL = 'https://d1dxpd4xe1iu0.cloudfront.net';

const CONTACTS = [
  {
    icon: 'call',
    url: 'tel:0334542911'
  },
  {
    icon: 'zalo',
    url: 'https://zalo.me/3040659602030459790'
  },
  {
    icon: 'messenger',
    url: 'https://www.messenger.com/t/107343460671817'
  }
]

const SaleProposal = ({ meta }: any) => {
  const trans = useTrans();
  const sliderRef = useRef<any>(null);
  const [languageSelected, setLanguageSelected] = useState('');
  const [isMobile, setIsMobile] = useState(false);
  const [showContacts, setShowContacts] = useState(false);

  const settings = {
    dots: false,
    infinite: true,
    slidesToShow: 1,
    slidesToScroll: 1,
    vertical: true,
    verticalSwiping: true,
  };

  useEffect(() => {
    if (window.innerWidth < 767) {
      setIsMobile(true);
    }
  }, []);

  return (
    <>
      <Meta {...(meta?.title ? meta : { title: trans.saleProposal })} />
      <div className={'h-dvh overflow-hidden'}>
        {!languageSelected ? (
          <div
            className={'flex h-full items-center justify-center bg-dark px-4'}
          >
            <div
              className={
                'w-full max-w-[772px] rounded-lg bg-secondaryGradient p-8'
              }
            >
              <h3 className={'text-32 text-blue md:text-24'}>
                Vui lòng chọn ngôn ngữ
              </h3>
              <h6 className={'mt-2 text-18 text-blue md:text-16'}>
                Please choose your languages
              </h6>
              <div className={'mt-6 flex gap-x-4'}>
                {LANGUAGES.map((lang) => (
                  <button
                    key={lang.id}
                    onClick={() => setLanguageSelected(lang.id)}
                    className={
                      'flex h-[150px] w-[164px] flex-col items-center justify-center gap-y-4 rounded-xl bg-white px-6 shadow-normal'
                    }
                  >
                    <AppImage
                      src={`/assets/images/${lang.icon}`}
                      className={'!h-12 !w-12 object-contain'}
                    />
                    <p className={'barlow'}>{lang.name}</p>
                  </button>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className={'relative h-full bg-secondaryGradient'}>
            {/* <div className={'fixed top-0 left-0 z-10'}> */}
            {/*  <input value={value} onChange={(e) => { */}
            {/*    console.log('1', e.currentTarget.value) */}
            {/*    setValue(e.currentTarget.value) */}
            {/*  }}/> */}
            {/*  <button onClick={() => { */}
            {/*    const pos = SLIDERS.findIndex(item => item === value) */}
            {/*    sliderRef?.current.slickGoTo(pos) */}
            {/*  }}> */}
            {/*    submit */}
            {/*  </button> */}
            {/* </div> */}
            {/* @ts-ignore */}
            <Slider {...settings} ref={sliderRef}>
              {Array.from({ length: 20 }, (_, index) => index + 1).map(
                (item) => (
                  <div className={'h-dvh'} key={item}>
                    <AppImage
                      src={`${AWS_URL}/${languageSelected}${isMobile ? '-mobile' : ''
                        }/slide-${item}.png`}
                      className={'!object-contain'}
                    />
                  </div>
                ),
              )}
            </Slider>
            <ul className={'absolute bottom-12 right-6 z-10 flex gap-x-4'}>
              <li
                onClick={() => sliderRef?.current?.slickPrev()}
                className={
                  'barlow flex aspect-square w-16 cursor-pointer items-center justify-center rounded-full bg-white text-20'
                }
              >
                <span className={'-translate-y-[2px] cursor-pointer'}>←</span>
              </li>
              <li
                onClick={() => sliderRef?.current?.slickNext()}
                className={
                  'barlow flex aspect-square w-16 cursor-pointer items-center justify-center rounded-full bg-white text-20'
                }
              >
                <span className={'-translate-y-[2px] cursor-pointer'}>→</span>
              </li>
            </ul>
            <div className="fixed bottom-2 right-6 z-20 flex flex-col gap-3">
              {showContacts && (
                <>
                  {
                    CONTACTS.map((contact, index) => (
                      <Button
                        key={index}
                        url={contact.url}
                        target='_blank'
                        className="hover:bg-blue-700 flex !h-14 !w-14 animate-fadeInDown items-center justify-center !rounded-full bg-blue !p-0 shadow-lg transition-all duration-300"
                      >
                        <Icon name={contact.icon} size={32} />
                      </Button>
                    ))
                  }
                </>
              )}
              <Button
                onClick={() => setShowContacts(!showContacts)}
                className={`${showContacts ? '!w-14' : '!w-[88px]'} flex !h-8 items-center justify-center !rounded-2xl bg-primary`}
              >
                <span className="text-2xl text-white">{showContacts ? '×' : 'Liên hệ'}</span>
              </Button>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export async function getServerSideProps(context: any) {
  const { req, resolvedUrl, locale } = context;
  try {
    const seoWebsitesRef = await getSeoWebsites({
      clientdomain: req.headers.host,
    });
    const seoWebsitesRefData = await seoWebsitesRef.json();
    const seoData =
      seoWebsitesRefData.data?.find((item: any) => item.page === resolvedUrl) ||
      {};
    return {
      props: {
        meta: formatMeta(seoData, locale),
      },
    };
  } catch (error) {
    console.log(getErrorMessage(error));
  }
  return {
    props: {},
  };
}

export default SaleProposal;

export const runtime = 'experimental-edge';
