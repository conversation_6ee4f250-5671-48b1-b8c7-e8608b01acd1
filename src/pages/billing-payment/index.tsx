'use client';

import { useRouter } from 'next/router';
import React from 'react';

import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import TextGradient from '@/components/TextGradient';
import Title from '@/components/Title';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const HEADER = {
  subTitle: {
    en: 'Billings, Invoices & Payments',
    vi: 'Phiếu thu, <PERSON><PERSON> đơn & <PERSON> toán',
  },
  title: {
    en: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Streamline'} className={'inline'} />{' '}
        {`your cash flow with easy tuition and fee collection.`}
      </h1>
    ),
    vi: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Chuẩn xác hoá'} className={'inline'} />{' '}
        {`dòng tiền của bạn bằng việc quản lý thu chi rõ ràng.`}
      </h1>
    ),
  },
  description: {
    en: 'Stop wasting valuable time by automatically collecting tuition and fees. Offer a convenient option for parents to pay when they want, how they want. Easily send, store, and organize invoices in one place. Plus, automate fee reminders to parents or never miss a payment with autopay.',
    vi: 'Tiết kiệm thời gian quý báu bằng việc tự động thu học phí và các khoản phí khác. Cung cấp các lựa chọn tiện lợi cho phụ huynh thanh toán theo ý muốn. Dễ dàng gửi, lưu trữ và sắp xếp hóa đơn tại một nơi. Ngoài ra, tự động hóa thông báo nhắc nhở về các khoản phí cho phụ huynh hoặc không bỏ lỡ một khoản thanh toán nào với tính năng tự động thanh toán.',
  },
};

const HIGHLIGHTS = [
  {
    title: {
      en: 'Schedule billing and automate tuition payments',
      vi: 'Lên lịch thanh toán và tự động yêu cầu thanh toán học phí',
    },
    description: {
      en: 'Let parents pay the way they want with credit card, debit card, or direct bank transfer (ACH). Families can pay directly from your family engagement app. Further, streamline your cash flow with recurring, scheduled billing.',
      vi: 'Hãy để phụ huynh thanh toán theo cách họ muốn bằng thẻ tín dụng, thẻ ghi nợ hoặc chuyển khoản ngân hàng trực tiếp (ACH). Phụ huynh có thể thanh toán trực tiếp trên ứng dụng Mầm Chồi Lá của phụ huynh. Hơn nữa, tối ưu hoá dòng tiền của bạn bằng cách thanh toán định kỳ, theo lịch thanh toán.',
    },
    images: ['principal_working.jpg', 'tuition_cms.jpeg'],
  },
  {
    title: {
      en: 'Batch charges or automate reminders',
      vi: 'Thông báo phí đồng loạt hoặc tự động nhắc nhở',
    },
    description: {
      en: 'Save valuable time. Bulk charge families at once and automate text messages and emails to remind families to pay tuition and other fees.',
      vi: 'Tiết kiệm thời gian quý báu. Thông báo phí hàng loạt cho các phụ huynh cùng một lúc và tự động hóa tin nhắn văn bản và email để nhắc nhở các gia đình thanh toán học phí và các khoản phí khác.',
    },
    images: ['notification_cms.jpeg', 'parent_checking.jpg'],
  },
  {
    title: {
      en: 'View childcare billing reports and track tuition in one place',
      vi: 'Xem báo cáo tài chính của trung tâm và theo dõi học phí cùng một nơi',
    },
    description: {
      en: `Bill and invoice entirely online, including recurring payments. All invoices are attached to family records or childcare billing profiles. Plus, quickly view reports on tuition payments.`,
      vi: 'Phiếu thu và hóa đơn hoàn toàn trực tuyến, bao gồm cả các khoản thanh toán định kỳ. Tất cả hóa đơn đều được đính kèm vào hồ sơ học sinh và phụ huynh hoặc hồ sơ thanh toán dịch vụ của trung tâm. Ngoài ra, còn có các báo cáo nhanh về thanh toán học phí theo tháng.',
    },
    images: ['overall_report.jpeg', 'tuition_detail.jpeg'],
  },
];

const BENEFITS = {
  title: {
    en: 'Benefits of using Digitalized Attendance Tracking',
    vi: 'Lợi ích của việc sử dụng Báo cáo học phí & giao dịch trực tuyến',
  },
  data: [
    {
      icon: 'lovely',
      text: {
        en: 'Get paid faster, every time',
        vi: 'Thu tiền học phí nhanh hơn, mọi lúc mọi nơi',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Save time with automated payments and fees',
        vi: 'Tiết kiệm thời gian với thông báo học phí tự động',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Manage and monitor invoices',
        vi: 'Quản lý và quan sát hoá đơn và phiếu thu trực tuyến',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Let parents pay the way they want',
        vi: 'Hổ tợ phụ huynh có thể thanh toán theo cách họ muốn',
      },
    },
  ],
};

const BillingPayment = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <Main meta={<Meta title={trans.billingPayment} description={''} />}>
      <section className={`${sectionClassName} pt-0`}>
        <div className={'grid grid-cols-2 lg:grid-cols-1 lg:gap-y-2'}>
          <div className={'space-y-10 md:space-y-6'}>
            <h5 className={`barlow mb-10 text-18 text-red underline`}>
              <b>{HEADER.subTitle[locale]}</b>
            </h5>
            {HEADER.title[locale]()}
            <p className={'mb-14 mt-10 max-w-[506px]'}>
              {HEADER.description[locale]}
            </p>
            <div
              className={'flex items-center gap-8 lg:flex-col lg:items-start'}
            >
              <AppLink href={'/get-quote'} text={trans.requestADemo} />
              <AppLink
                href={'/principals-management'}
                text={trans.seeItInAction}
              />
            </div>
          </div>
          <AppImage
            src="/assets/images/billing-payment/header.png"
            className={'!object-contain lg:mx-auto lg:max-w-[480px] md:hidden'}
          />
        </div>
      </section>

      <section className={sectionClassName}>
        <div className={'space-y-[112px] md:space-y-12'}>
          {HIGHLIGHTS.map((box, index) => (
            <div key={index} className={'space-y-12 md:space-y-6'}>
              <div
                className={'max-w-[70%] space-y-6 lg:max-w-full md:space-y-4'}
              >
                <Title text={box.title[locale]} />
                <p className={'barlow'}>{box.description[locale]}</p>
              </div>
              <div className={'grid grid-cols-2 gap-x-6 md:gap-x-4'}>
                {box.images.map((img, idx) => (
                  <div key={idx} className={'aspect-[640/400]'}>
                    <AppImage
                      src={`/assets/images/billing-payment/${img}`}
                      className={'rounded-lg'}
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      <section className={`${sectionClassName} max-w-[1600px]`}>
        <h5
          className={`barlow mb-16 text-center text-18 text-red underline md:mb-6 md:text-left`}
        >
          <b>{BENEFITS.title[locale]}</b>
        </h5>
        <div
          className={
            'grid grid-cols-4 gap-6 lg:grid-cols-2 md:grid-cols-1 md:gap-y-4 md:px-0'
          }
        >
          {BENEFITS.data.map((item, index) => (
            <div
              key={index}
              className={
                'flex flex-col items-center gap-y-6 rounded-2xl bg-white p-12 text-center shadow-normal md:flex-row md:gap-x-4 md:p-6 md:text-left'
              }
            >
              <div
                className={
                  'flex h-12 w-12 items-center justify-center rounded-full bg-blue/10'
                }
              >
                <Icon name={item.icon} className={'fill-blue'} />
              </div>
              <p>
                <b>{item.text[locale]}</b>
              </p>
            </div>
          ))}
        </div>
      </section>

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export default BillingPayment;
