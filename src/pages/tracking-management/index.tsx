'use client';

import { useRouter } from 'next/router';
import React from 'react';

import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Button from '@/components/Button';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import TextGradient from '@/components/TextGradient';
import Title from '@/components/Title';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const HEADER = {
  subTitle: {
    en: 'Guardians tracking & management',
    vi: '<PERSON>ụ huynh theo dõi & quản lý',
  },
  title: {
    en: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Streamline'} className={'inline'} />{' '}
        {` check-in and attendance tracking.`}
      </h1>
    ),
    vi: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Tối ưu hoá'} className={'inline'} />{' '}
        {`việc đưa đón và theo dõi điểm danh.`}
      </h1>
    ),
  },
  description: {
    en: 'Tracking your child information in one place to monitor attendance classroom, and share authorized pickups. Record and organized child data in real-time. Maintain health and safety by knowing where kids are always.',
    vi: 'Theo dõi thông tin về con cái tại một ứng dụng để giám sát sự có mặt trong lớp học và chia sẻ việc đón trẻ được ủy quyền an toàn. Cập nhật và tổ chức dữ liệu về trẻ theo thời gian thực. Báo cáo tình trạng sức khỏe và an toàn của trẻ mọi lúc, mọi nơi.',
  },
};

const HIGHLIGHTS = [
  {
    title: {
      en: 'AI Check-in Camera',
      vi: 'Camera điểm danh thông minh bằng AI',
    },
    description: {
      en: 'We use AI check-in camera to record attendance with fast and accurate.',
      vi: 'Chúng tôi sử dụng camera điểm danh AI để ghi nhận việc ra / vào của trẻ nhanh chóng và chính xác.',
    },
    images: ['camera_ai.png', 'checkedin_cms.png'],
  },
  {
    title: {
      en: 'Digitized Drop-off & Pick-up',
      vi: 'Lưu lại lịch sử đưa / đón trẻ bằng kỹ thuật số',
    },
    description: {
      en: 'Allow families to check-in digitally. Plus, guardians can quickly share authorized pickup code for others and teachers was informed and verify it again through easy-to use app.',
      vi: 'Cho phép các phụ huynh xin nghỉ phép trực tuyến. Ngoài ra, phụ huynh có thể nhanh chóng chia sẻ mã đón trẻ được ủy quyền cho người khác và giáo viên đã được thông báo cũng như xác minh lại mã đó thông qua ứng dụng một cách dễ dàng.',
    },
    images: ['pick_up.jpg', 'authorized_pickup.png'],
  },
  {
    title: {
      en: 'Easily track & report on attendance',
      vi: 'Dễ dàng theo dõi và báo cáo việc điểm danh',
    },
    description: {
      en: 'Gain insight from real-time reporting. Automatically evaluate tuition based on kid scheduled attendance. Ensure safety by knowing where the kids are all the times.',
      vi: 'Có được thông tin chính xác từ báo cáo thời gian thực. Tự động tính toán học phí dựa trên lịch trình đi học của trẻ. Đảm bảo an toàn bằng cách luôn để trẻ trong tầm mắt của bạn.',
    },
    images: ['attendance_check.png', 'school_day_check.png'],
  },
];

const BENEFITS = {
  title: {
    en: 'Benefits of using Digitalized Attendance Tracking',
    vi: 'Lợi ích của việc sử dụng Điểm danh kỹ thuật số',
  },
  data: [
    {
      icon: 'lovely',
      text: {
        en: 'Save more time with digitized attendance',
        vi: 'Tiết kiệm nhiều thời gian hơn với việc điểm danh kỹ thuật số',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Minimized manual tasks for teachers & parents',
        vi: 'Giảm thiểu các công việc thủ công cho giáo viên và phụ huynh',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Gain quickly insight from automated attendance reports',
        vi: 'Thu thập thông tin nhanh chóng từ các báo cáo điểm danh tự động',
      },
    },
  ],
};

const TrackingManagement = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <Main meta={<Meta title={trans.trackingManagement} description={''} />}>
      <section className={`${sectionClassName} pt-0`}>
        <div className={'grid grid-cols-[64%_auto] lg:grid-cols-1 lg:gap-y-2'}>
          <div className={'space-y-10 md:space-y-6'}>
            <h5 className={`barlow mb-10 text-18 text-red underline`}>
              <b>{HEADER.subTitle[locale]}</b>
            </h5>
            {HEADER.title[locale]()}
            <p className={'mb-14 mt-10 max-w-[506px]'}>
              {HEADER.description[locale]}
            </p>
            <div
              className={'flex items-center gap-8 lg:flex-col lg:items-start'}
            >
              <Button className={'!rounded-[30px] bg-white'}>
                <>
                  <span className={'text-dark'}>{trans.inviteYourSchool}</span>
                  <Icon name={'export-circle'} className={'fill-dark'} />
                </>
              </Button>
              <AppLink
                href={'/parents-application'}
                text={trans.seeItInAction}
              />
            </div>
          </div>
          <AppImage
            src="/assets/images/tracking-management/header.png"
            className={'!object-contain lg:mx-auto lg:max-w-[480px] md:hidden'}
          />
        </div>
      </section>

      <section className={sectionClassName}>
        <div className={'space-y-[112px] md:space-y-12'}>
          {HIGHLIGHTS.map((box, index) => (
            <div key={index} className={'space-y-12 md:space-y-6'}>
              <div
                className={'max-w-[70%] space-y-6 lg:max-w-full md:space-y-4'}
              >
                <Title text={box.title[locale]} />
                <p className={'barlow'}>{box.description[locale]}</p>
              </div>
              <div className={'grid grid-cols-2 gap-x-6 md:gap-x-4'}>
                {box.images.map((img, idx) => (
                  <div key={idx} className={'aspect-[640/400]'}>
                    <AppImage
                      src={`/assets/images/tracking-management/${img}`}
                      className={'rounded-lg'}
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      <section className={sectionClassName}>
        <h5
          className={`barlow mb-16 text-center text-18 text-red underline md:mb-6 md:text-left`}
        >
          <b>{BENEFITS.title[locale]}</b>
        </h5>
        <div
          className={
            'grid grid-cols-3 gap-x-6 px-20 lg:px-0 md:grid-cols-1 md:gap-y-4'
          }
        >
          {BENEFITS.data.map((item, index) => (
            <div
              key={index}
              className={
                'flex flex-col items-center gap-y-6 rounded-2xl bg-white p-12 text-center shadow-normal md:flex-row md:gap-x-4 md:rounded-lg md:p-6 md:text-left'
              }
            >
              <div
                className={
                  'flex h-12 w-12 items-center justify-center rounded-full bg-blue/10'
                }
              >
                <Icon name={item.icon} className={'fill-blue'} />
              </div>
              <p className={'flex-1'}>
                <b>{item.text[locale]}</b>
              </p>
            </div>
          ))}
        </div>
      </section>

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export default TrackingManagement;
