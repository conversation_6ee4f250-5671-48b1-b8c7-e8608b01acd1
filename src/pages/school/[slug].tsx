import { useRouter } from 'next/router';
import { useEffect } from 'react';

const School = () => {
  const { pathname, query, replace, push } = useRouter();

  const isValidDomainName = (domain: string) => {
    // Define a regular expression for a simple domain name format
    const domainRegex = /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

    // Test the given string against the regular expression
    return domainRegex.test(domain);
  };

  useEffect(() => {
    if (pathname.includes('/school/')) {
      const schoolDomainName = isValidDomainName(query?.slug as string)
        ? `https://${query?.slug}`
        : `https://${query?.slug}.mamchoila.com.vn`;
      if (query?.tuitionId) {
        replace(`${schoolDomainName}/tuition/${query.tuitionId}`);
      }

      if (query?.roleId) {
        replace(`${schoolDomainName}/attendance-quantity/${query.roleId}`);
      }

      if (query?.candidateId) {
        replace(
          `${schoolDomainName}/admission-information/${query.candidateId}`,
        );
      }
      if (query?.payslipId) {
        replace(`${schoolDomainName}/pay-slip/${query.payslipId}`);
      }
    } else {
      push('/');
    }
  }, [query, isValidDomainName]);

  return <h1 />;
};

// export async function getServerSideProps(context: any) {
//   console.log('referer', context.req.headers.referer);
//   return {
//     props: {},
//   };
// }
export default School;
