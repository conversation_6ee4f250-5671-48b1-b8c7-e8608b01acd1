'use client';

import { useRouter } from 'next/router';
import React from 'react';

import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Button from '@/components/Button';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import TextGradient from '@/components/TextGradient';
import Title from '@/components/Title';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const HEADER = {
  subTitle: {
    en: 'Classroom management',
    vi: 'Quản lý lớp học',
  },
  title: {
    en: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Streamline'} className={'inline'} />{' '}
        {`check-in and attendance tracking.`}
      </h1>
    ),
    vi: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Dễ dàng'} className={'inline'} />{' '}
        {`quản lý và theo dõi từng trẻ trong lớp.`}
      </h1>
    ),
  },
  description: {
    en: 'Track all child information in one place to monitor attendance classroom ratios, and approved pick-ups. Record and organize child data in real-time. Maintain health and safety by knowing where students are always.',
    vi: 'Theo dõi tất cả thông tin của học sinh tại một một ứng dụng để theo dõi tỷ lệ đi học, đảm bảo an toàn khi đưa đón trẻ. Cập nhật và sắp xếp dữ liệu về trẻ theo thời gian thực. Báo cáo tình trạng sức khỏe và an toàn của học sinh mọi lúc.',
  },
};

const HIGHLIGHTS = [
  {
    title: {
      en: 'Fill open seats and maintain ratio compliance',
      vi: 'Luôn luôn quan sát tỷ lệ học sinh',
    },
    description: {
      en: 'Easily ensure your classroom ratios remain compliant with state licensing laws. Monitor open seats with the click of a button.',
      vi: 'Dễ dàng đảm bảo tỷ lệ lớp học của bạn vẫn tuân thủ luật cấp phép đúng luật. Giám sát toàn bộ số lượng học sinh chỉ bằng một chạm.',
    },
    images: ['class_attendance.jpg', 'attendance_calendar.jpg'],
  },
  {
    title: {
      en: 'Update real-time to parents',
      vi: 'Cập nhật liên tục và nhanh chóng cho phụ huynh',
    },
    description: {
      en: 'Allow families to check students in digitally and inform teachers of any important drop-off notes through an easy-to use app. Plus, guardians can quickly let staff know their estimated time of arrival at pick up time.',
      vi: 'Allow families to check students in digitally and inform teachers of any important drop-off notes through an easy-to use app. Plus, guardians can quickly let staff know their estimated time of arrival at pick up time.',
    },
    images: ['tempurature_check.jpg', 'photo_album.jpeg'],
  },
  {
    title: {
      en: 'Health check and body index periodically monthly',
      vi: 'Kiểm tra sức khoẻ và chỉ số cơ thể định kỳ hàng tháng',
    },
    description: {
      en: 'Gain insight from real-time reporting. Automatically evaluate tuition for each child based on their scheduled attendance. Ensure safety by knowing where students are at all times. Plus, easily track staff hours to monitor payroll.',
      vi: 'Gain insight from real-time reporting. Automatically evaluate tuition for each child based on their scheduled attendance. Ensure safety by knowing where students are at all times. Plus, easily track staff hours to monitor payroll.',
    },
    images: ['bmi_check.jpg', 'bmi_check_app.jpeg'],
  },
];

const BENEFITS = {
  title: {
    en: 'Benefits of using Digital Class Management',
    vi: 'Benefits of using Digital Class Management',
  },
  data: [
    {
      icon: 'lovely',
      text: {
        en: 'Keep information up-to-date',
        vi: 'Keep information up-to-date',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Boost family satisfaction',
        vi: 'Boost family satisfaction',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Save time',
        vi: 'Save time',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Stay proactive',
        vi: 'Stay proactive',
      },
    },
  ],
};

const ClassroomManagement = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <Main meta={<Meta title={trans.classroomManagement} description={''} />}>
      <section className={`${sectionClassName} pt-0`}>
        <div className={'grid grid-cols-[64%_auto] lg:grid-cols-1 lg:gap-y-2'}>
          <div className={'space-y-10 md:space-y-6'}>
            <h5 className={`barlow mb-10 text-18 text-red underline`}>
              <b>{HEADER.subTitle[locale]}</b>
            </h5>
            {HEADER.title[locale]()}
            <p className={'mb-14 mt-10 max-w-[506px]'}>
              {HEADER.description[locale]}
            </p>
            <div
              className={'flex items-center gap-8 lg:flex-col lg:items-start'}
            >
              <Button className={'!rounded-[30px] bg-white'}>
                <>
                  <span className={'text-dark'}>{trans.inviteYourSchool}</span>
                  <Icon name={'export-circle'} className={'fill-dark'} />
                </>
              </Button>
              <AppLink
                href={'/educators-application'}
                text={trans.seeItInAction}
              />
            </div>
          </div>
          <AppImage
            src="/assets/images/classroom-management/header.png"
            className={'!object-contain lg:mx-auto lg:max-w-[480px] md:hidden'}
          />
        </div>
      </section>

      <section className={sectionClassName}>
        <div className={'space-y-[112px] md:space-y-12'}>
          {HIGHLIGHTS.map((box, index) => (
            <div key={index} className={'space-y-12 md:space-y-6'}>
              <div
                className={'max-w-[70%] space-y-6 lg:max-w-full md:space-y-4'}
              >
                <Title text={box.title[locale]} />
                <p className={'barlow'}>{box.description[locale]}</p>
              </div>
              <div className={'grid grid-cols-2 gap-x-6 md:gap-x-4'}>
                {box.images.map((img, idx) => (
                  <div key={idx} className={'aspect-[640/400]'}>
                    <AppImage
                      src={`/assets/images/classroom-management/${img}`}
                      className={'rounded-lg'}
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      <section className={`${sectionClassName} max-w-[1600px]`}>
        <h5
          className={`barlow mb-16 text-center text-18 text-red underline md:mb-6 md:text-left`}
        >
          <b>{BENEFITS.title[locale]}</b>
        </h5>
        <div
          className={
            'grid grid-cols-4 gap-6 lg:grid-cols-2 md:grid-cols-1 md:gap-y-4 md:px-0'
          }
        >
          {BENEFITS.data.map((item, index) => (
            <div
              key={index}
              className={
                'flex flex-col items-center gap-y-6 rounded-2xl bg-white p-12 text-center shadow-normal md:flex-row md:gap-x-4 md:p-6 md:text-left'
              }
            >
              <div
                className={
                  'flex h-12 w-12 items-center justify-center rounded-full bg-blue/10'
                }
              >
                <Icon name={item.icon} className={'fill-blue'} />
              </div>
              <p>
                <b>{item.text[locale]}</b>
              </p>
            </div>
          ))}
        </div>
      </section>

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export default ClassroomManagement;
