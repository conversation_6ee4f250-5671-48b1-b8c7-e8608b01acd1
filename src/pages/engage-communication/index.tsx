'use client';

import { useRouter } from 'next/router';
import React from 'react';

import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Button from '@/components/Button';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import TextGradient from '@/components/TextGradient';
import Title from '@/components/Title';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const HEADER = {
  subTitle: {
    en: 'Parents engagement & communication',
    vi: 'Sự tương tác và giao tiếp của phụ huynh',
  },
  title: {
    en: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Build'} className={'inline'} />{' '}
        {`meaningful relationships with your enrolled families.`}
      </h1>
    ),
    vi: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Xây dựng'} className={'inline'} />{' '}
        {`mối quan hệ gắn kết với các phụ huynh hiện tại của bạn.`}
      </h1>
    ),
  },
  description: {
    en: "Today's families and staff expect real-time communication and easy-to-use engagement tools. Close the gap between teachers and guardians while offering a seamless experience with your brand.",
    vi: 'Ngày nay, gia đình lẫn nhà trường đều mong muốn giao tiếp với nhau một cách nhanh chóng và sử dụng các công cụ tương tác dễ dàng. Hãy thu hẹp khoảng cách giữa giáo viên và phụ huynh, đồng thời mang đến một trải nghiệm hoàn toàn liền mạch với cho trung tâm củ bạn.',
  },
};

const HIGHLIGHTS = [
  {
    id: 1,
    title: {
      en: 'Bridge the gap between teachers and families',
      vi: 'Thu hẹp khoảng cách giữa giáo viên và phụ huynh',
    },
    description: {
      en: 'Allow educators to connect with families in real-time and keep them ‘in the know’ throughout their child’s day.',
      vi: 'Cho phép các giáo viên/bảo mẫu kết nối với các phụ huynh theo thời gian thực và luôn cập nhật cho gia đình mọi thông tin trong suốt cả ngày của trẻ.',
    },
    listing: [
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Family Engagement App.</span> Give
            families confidence about their care choices via a secure, tailored
            app that lets you deliver all the details they need, from billing
            info to the true value of play-based learning and everything in
            between.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>
              Ứng dụng tương tác của Phụ huynh.
            </span>{' '}
            Mang lại cho các phụ huynh sự an tâm về các lựa chọn chăm sóc trẻ
            thông qua một ứng dụng an toàn, phù hợp cho phép bạn cung cấp tất cả
            thông tin chi tiết gia đình cần, từ thông tin thanh toán đến giá trị
            thực sự của việc học tập dựa trên vui chơi và mọi thứ liên quan.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Family Portal.</span> Enable
            enrolled families to manage tuition payments, track their child’s
            developmental milestones, review meal menus, and more in one central
            hub.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>
              Cổng thông tin dành cho gia đình.
            </span>{' '}
            Cho phép các phụ huynh quản lý việc thanh toán học phí, theo dõi các
            mốc phát triển của trẻ, xem lại thực đơn bữa ăn, v.v. tại một cổng
            thông tin duy nhất.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Activity Tracking.</span> Provide
            teachers with the ability to update families throughout the child’s
            day by sharing daily activities and developmental observations.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>Theo dõi hoạt động.</span> Các giáo
            viên/bảo mẫu sẽ cập nhật thông tin cho gia đình trong suốt cả ngày
            của trẻ bằng cách chia sẻ các hoạt động hàng ngày và quan sát sự
            phát triển của trẻ.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Photo Gallery.</span> Allow
            families to view photos and videos throughout their child’s day and
            share these special moments with other family members and friends.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>Hình ảnh.</span> Cho phép các gia
            đình xem ảnh và video trong ngày của trẻ và chia sẻ những khoảnh
            khắc đặc biệt này với các thành viên khác trong gia đình và bạn bè.
          </>
        ),
      },
    ],
    images: ['communication.jpg', 'photo_album.png'],
  },
  {
    id: 2,
    title: {
      en: 'Keep parents in the loop about their child’s care',
      vi: 'Luôn thông báo cho phụ huynh về tình hình sức khoẻ và phát triển của trẻ',
    },
    description: {
      en: 'Provide families with the tools they need to reinforce their child’s curriculum and support ongoing developmental progress.',
      vi: 'Cung cấp cho gia đình những công cụ cần thiết để tham khảo các chương trình giảng dạy và hỗ trợ quá trình phát triển liên tục của trẻ.',
    },
    listing: [
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Curriculum Sharing.</span> Allow
            teachers to share lesson plans with less work by classroom or age
            group to ensure families can help reinforce curriculum at home.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>
              Chia sẻ chương trình giảng dạy.
            </span>{' '}
            Cho phép giáo viên chia sẻ giáo án trực tuyến theo lớp học hoặc nhóm
            tuổi để đảm bảo gia đình có thể tham khảo chương trình giảng dạy ở
            nhà.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Meal Planning.</span> Make things
            easier for staff by allowing them to share menus with families in
            advance - across classrooms, age groups, and more.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>Kế hoạch bữa ăn.</span> Giúp nhân
            viên dễ dàng sắp xếp hơn bằng cách cho phép chia sẻ trước thực đơn
            với gia đình - phân chia các lớp học, nhóm tuổi, v.v.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Daily Reports.</span> Save staff
            time and say goodbye to paper-based daily sheets with automated,
            summary emails that help families support development at home.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>Báo cáo hằng ngày.</span> Tiết kiệm
            thời gian của nhân viên và nói lời tạm biệt với các tờ giấy hàng
            ngày bằng các email tóm tắt, tự động giúp các phụ huynh theo dõi sự
            phát triển của trẻ tại nhà.
          </>
        ),
      },
    ],
    images: ['curriculum.jpg', 'lunch_menu.jpg'],
  },
  {
    id: 3,
    title: {
      en: "Monitor students' developmental growth",
      vi: 'Theo dõi sự phát triển của trẻ',
    },
    description: {
      en: 'Easily review and share children’s developmental growth and learning milestones, plus automatically share these with families at consistent intervals.',
      vi: 'Dễ dàng xem xét và chia sẻ các mốc phát triển và học tập của trẻ, đồng thời tự động chia sẻ những cột mốc này với gia đình theo những khoảng thời gian nhất quán.',
    },
    listing: [
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Digital Portfolios.</span> Provide
            staff with peace of mind before and during family-teacher
            conferences with automated portfolios that reflect the growth of the
            children in their care.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>Hồ sơ giảng dạy kỹ thuật số.</span>{' '}
            Giúp nhân viên yên tâm trước và trong các cuộc họp gia đình-giáo
            viên với lịch sử hồ sơ giảng dạy tự động phản ánh sự phát triển của
            trẻ mà họ đang chăm sóc.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Developmental Observations.</span>{' '}
            Share children’s developmental progress with families and track
            whether each child has met the learning standard set forth by your
            state, center, or organization.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>Quan sát sự phát triển.</span> Chia
            sẻ tiến trình phát triển của trẻ với gia đình và theo dõi xem mỗi
            trẻ có đáp ứng tiêu chuẩn học tập do phụ huynh, trung tâm hoặc tổ
            chức của bạn đặt ra hay không.
          </>
        ),
      },
    ],
    images: ['bmi.jpg', 'subjects.jpg'],
  },
  {
    id: 4,
    title: {
      en: 'Gather feedback from families with ease',
      vi: 'Thu thập phản hồi từ các gia đình một cách dễ dàng',
    },
    description: {
      en: 'Gain valuable insights and feedback from families without having to manually send multiple communications.',
      vi: 'Nhận được những báo cáo và phản hồi có giá trị từ gia đình mà không cần phải gửi nhiều thông tin liên lạc theo cách thủ công.',
    },
    listing: [
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Digital Newsfeed.</span> Provide
            teachers with the ability to update families throughout the child’s
            day. Plus, families can reply and react to updates to inform staff
            of the impact they’re making.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>
              Cập nhật thông tin kỹ thuật số.
            </span>{' '}
            Cung cấp cho giáo viên khả năng cập nhật thông tin cho gia đình
            trong suốt ngày của trẻ. Ngoài ra, các phụ huynh có thể trả lời và
            phản hồi các thông tin cập nhật để thông báo cho nhân viên về các
            hoạt động đang diễn ra.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Unlimited Messaging.</span> Send
            communications as much as families want or need. Use one family
            benefits platform for guidance, to increase retention and ensure
            messages don’t get overlooked.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>Không giới hạn tin nhắn.</span> Gửi
            thông tin liên lạc bất kể khi nào phụ huynh muốn hoặc cần. Sử dụng
            một nền để tăng quyền lợi dành cho phụ huynh để được kết nối, tăng
            cường khả năng tương tác và đảm bảo thông điệp không bị bỏ qua.
          </>
        ),
      },
    ],
    images: ['health.jpg', 'class.jpg'],
  },
];

const EngageCommunication = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <Main meta={<Meta title={trans.engageCommunication} description={''} />}>
      <section className={`${sectionClassName} pt-0`}>
        <div className={'grid grid-cols-[64%_auto] lg:grid-cols-1 lg:gap-y-2'}>
          <div className={'space-y-10 md:space-y-6'}>
            <h5 className={`barlow mb-10 text-18 text-red underline`}>
              <b>{HEADER.subTitle[locale]}</b>
            </h5>
            {HEADER.title[locale]()}
            <p className={'mb-14 mt-10 max-w-[506px]'}>
              {HEADER.description[locale]}
            </p>
            <div
              className={'flex items-center gap-8 lg:flex-col lg:items-start'}
            >
              <Button className={'!rounded-[30px] bg-white'}>
                <>
                  <span className={'text-dark'}>{trans.inviteYourSchool}</span>
                  <Icon name={'export-circle'} className={'fill-dark'} />
                </>
              </Button>
              <AppLink
                href={'/parents-application'}
                text={trans.seeItInAction}
              />
            </div>
          </div>
          <AppImage
            src="/assets/images/engage-communication/header.png"
            className={'!object-contain lg:mx-auto lg:max-w-[480px] md:hidden'}
          />
        </div>
      </section>

      <section className={sectionClassName}>
        <div className={'space-y-[112px] md:space-y-12'}>
          {HIGHLIGHTS.map((box, index) => (
            <div key={index}>
              <div
                className={'max-w-[70%] space-y-6 lg:max-w-full md:space-y-4'}
              >
                <Title text={box.title[locale]} />
                <p className={'barlow'}>{box.description[locale]}</p>
              </div>
              <div
                className={'mb-12 mt-6 grid grid-cols-4 gap-6 lg:grid-cols-2'}
              >
                {box.listing.map((item, i) => (
                  <p key={i} className={'barlow'}>
                    {item[locale]()}
                  </p>
                ))}
              </div>
              <div className={'grid grid-cols-2 gap-x-6 md:gap-x-4'}>
                {box.images.map((img, idx) => (
                  <div key={idx} className={'aspect-[640/400]'}>
                    <AppImage
                      src={`/assets/images/engage-communication/${img}`}
                      className={'rounded-lg'}
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export default EngageCommunication;
