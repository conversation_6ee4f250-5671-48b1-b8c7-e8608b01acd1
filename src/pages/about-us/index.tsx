'use client';

import Link from 'next/link';
import { useRouter } from 'next/router';
import React from 'react';

import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Icon from '@/components/Icon';
import Title from '@/components/Title';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';
import { formatDescription } from '@/utils';

const SOCIALS = [
  {
    icon: 'facebook',
    url: 'https://www.facebook.com/mamchoilaonline',
  },
  {
    icon: 'instagram',
    url: 'https://www.instagram.com/mamchoilavn/',
  },
  {
    icon: 'x',
    url: 'https://twitter.com/Mamchoila',
  },
  {
    icon: 'youtube',
    url: 'https://www.youtube.com/@mamchoilaonline',
  },
  {
    icon: 'tiktok',
    url: 'https://www.tiktok.com/@mamchoilaonline',
  },
  {
    icon: 'pinterest',
    url: 'https://www.pinterest.com/mamchoila/',
  },
];

const HEADER = {
  title: {
    en: () => (
      <>
        <span className={`bg-primaryGradient bg-clip-text text-transparent`}>
          Hi,{' '}
        </span>
        we are <br /> Mầm Chồi Lá.
      </>
    ),
    vi: () => (
      <>
        <span className={`bg-primaryGradient bg-clip-text text-transparent`}>
          Xin chào,{' '}
        </span>
        tụi mình là Mầm Chồi Lá.
      </>
    ),
  },
  description: {
    en: 'Our software platform simplifies the complicated world of childcare marketing, registration, payment processing, and operations so childcare staff can spend time on their most important job, taking care of the children and families in their care.',
    vi: 'Nền tảng phần mềm của Mầm Chồi Lá đơn giản hóa thế giới phức tạp về tưởng tác, tuyển sinh, xử lý thanh toán và vận hành trung tâm giáo dục mầm non để nhân viên chăm sóc trẻ có thể dành thời gian cho công việc quan trọng nhất của họ là chăm sóc trẻ mà các gia đình đã giao cho trung tâm.',
  },
};

const INSPIRED = {
  title: {
    en: 'What’s inspire us',
    vi: 'Cảm hứng của Mầm Chồi Lá',
  },
  description: {
    en: 'Mầm Chồi Lá is based on extensive research and interviews with early childhood educators. We have learned a lot about early childhood education since we originally came together in 2012. However, the following learnings are what motivate and inspire us to always be better and do better:\n\n 1. The formative years from age 0 to 5 are critical to a child’s pfe-long development and most people are unaware of this well-researched fact\n\n2. The resources invested in early childhood education do not pgn to the importance of a child’s development (see learning 1 above)\n\n 3. The role of the early childhood educator is highly under-appreciated (see learning 1 above) and extremely challenging (see learning 2 above)\n\nOur goal is to empower early childhood educators with affordable tools that enable them to improve developmental outcomes for the children they work with while educating parents about the importance of their work.',
    vi: 'Mầm Chồi Lá dựa trên nghiên cứu sâu rộng và phỏng vấn các nhà giáo dục mầm non. Chúng tôi đã học được rất nhiều điều về giáo dục mầm non kể từ khi chúng tôi hợp tác lần đầu vào năm 2019. Tuy nhiên, những điều học được sau đây chính là động lực và truyền cảm hứng để chúng tôi luôn trở nên tốt hơn và làm tốt hơn:\n\n 1. Những năm tháng hình thành từ 0 đến 5 tuổi rất quan trọng đối với sự phát triển suốt đời của trẻ và hầu hết mọi người đều không biết về thực tế đã được nghiên cứu kỹ lưỡng này.\n\n2. Nguồn lực đầu tư vào giáo dục mầm non hiện tại chưa đủ với tầm quan trọng của sự phát triển của trẻ.\n\n3. Vai trò của nhà giáo dục mầm non bị đánh giá thấp và cực kỳ thách thức.\n\n Mục tiêu của chúng tôi là trao quyền cho các giáo viên mầm non những công cụ hợp lý cho phép họ cải thiện kết quả phát triển cho trẻ mà họ đang chăm sóc đồng thời giáo dục phụ huynh về tầm quan trọng của công việc của họ và giúp phụ huynh luôn luôn đồng hành trong giai đoạn phát triển đầu đời của trẻ.',
  },
};

const WHO_WE_ARE = {
  title: {
    en: 'Who we are',
    vi: 'Đội ngũ Mầm Chồi Lá là ai?',
  },
  description: {
    en: 'Mầm Chồi Lá is made up of a group of committed fathers, mothers, daughters, sons, uncles and aunts who are working hard to build a product and a company that families and early childhood educators love. We are a Certified B Corporation® which means that we meet the highest standards of verified social and environmental performance, transparency and accountability as part of a global movement of people using business as a force for good. The B stands for B the Change, inspired by Gandhi’s famous invocation that we must be the change that we seek in the world, and when we think about the world that we want today’s youngest children to grow and thrive in, we want to do our part to make that world a better place.',
    vi: 'Mầm Chồi Lá được tạo thành từ một nhóm những người cha, người mẹ, con gái, con trai, chú, dì, những người đang làm việc chăm chỉ để xây dựng một sản phẩm và một công ty mà các gia đình và các giáo viên mầm non yêu thích. Chúng tôi là một Certified B Corporation®, có nghĩa là chúng tôi đáp ứng các tiêu chuẩn cao nhất về hiệu quả hoạt động xã hội và môi trường, tính minh bạch và trách nhiệm giải trình đã được xác minh như một phần của phong trào toàn cầu về việc mọi người sử dụng doanh nghiệp như một động lực vì mục đích tốt đẹp. Chữ B là viết tắt của B the Change, lấy cảm hứng từ lời kêu gọi nổi tiếng của Gandhi rằng chúng ta phải là sự thay đổi mà chúng ta tìm kiếm trên thế giới và khi chúng ta nghĩ về thế giới mà chúng ta muốn những đứa trẻ nhỏ nhất ngày nay lớn lên và phát triển, chúng ta muốn làm điều đó một phần để làm cho thế giới đó trở thành một nơi tốt đẹp hơn.',
  },
  urLText: {
    en: 'Meet the Leadership team',
    vi: 'Gặp gỡ đội ngũ quản lý',
  },
};

const VALUES = {
  title: {
    en: 'Our core values',
    vi: 'Giá trị cốt lõi của Mầm Chồi Lá',
  },
  data: [
    {
      title: {
        en: 'Be a Good Person.',
        vi: 'Hãy là một người tốt.',
      },
      description: {
        en: 'We genuinely care for ourselves and others and show this in our daily interactions. We aim to foster a welcoming environment that cherishes diversity and empathy for all.',
        vi: 'Chúng tôi thực sự quan tâm đến bản thân và người khác và thể hiện điều này trong những tương tác hàng ngày. Chúng tôi mong muốn thúc đẩy một môi trường thân thiện, trân trọng sự đa dạng và đồng cảm với tất cả mọi người.',
      },
      image: '/assets/images/about-image.jpg',
    },
    {
      title: {
        en: 'Work Hard and Win.',
        vi: 'Hãy chăm chỉ và nhận thành quả.',
      },
      description: {
        en: 'We proudly roll up our sleeves and do the work that needs to be done. We take pride in every interaction, and keep our eye on the prize to deliver exciting results.',
        vi: 'Chúng tôi tự hào xắn tay áo lên và làm những công việc cần phải làm. Chúng tôi tự hào về mọi hoạt động tương tác và luôn chú trọng đến thành quả để mang lại kết quả thú vị.',
      },
      image: '/assets/images/about-image.jpg',
    },
    {
      title: {
        en: 'Own Positive Change.',
        vi: 'Hãy tích cực thay đổi bản thân.',
      },
      description: {
        en: 'We take every opportunity to make an impact at work, in our lives, and for our society. We embody a growth mindset and proudly celebrate growth and progress.',
        vi: 'Chúng tôi tận dụng mọi cơ hội để tạo ảnh hưởng trong công việc, trong cuộc sống và cho xã hội của chúng tôi. Chúng tôi thể hiện tư duy phát triển và tự hào tôn vinh sự phát triển và tiến bộ.',
      },
      image: '/assets/images/about-image.jpg',
    },
  ],
};

const FIND_US = {
  title: {
    en: 'Where to find us',
    vi: 'Liên hệ với Mầm Chồi Lá',
  },
};

const AboutUs = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <Main meta={<Meta title={trans.aboutUs} description={''} />}>
      <section className={sectionClassName}>
        <div
          className={
            'grid grid-cols-[min(40%,506px)_auto] gap-x-[104px] lg:grid-cols-1 lg:gap-y-6'
          }
        >
          <div className={'space-y-10 md:space-y-4'}>
            <h1 className={`barlow text-72 lg:text-48 md:text-40`}>
              {HEADER.title[locale]()}
            </h1>
            <p className={`barlow`}>{HEADER.description[locale]}</p>
          </div>
          <div>
            <AppImage
              src={'/assets/images/help_banner.jpg'}
              className={'rounded-2xl md:rounded-lg'}
            />
          </div>
        </div>
      </section>

      {/* Inspired Section */}
      <section className={sectionClassName}>
        <div
          className={
            'grid grid-cols-[min(40%,506px)_auto] gap-6 lg:grid-cols-1'
          }
        >
          <div className={'lg:order-last'}>
            <AppImage
              src={'/assets/images/about-image.jpg'}
              className={'mt-20 rounded-2xl lg:mt-0'}
            />
          </div>
          <div className={'space-y-6'}>
            <Title text={INSPIRED.title[locale]} />
            <p
              className={'barlow'}
              dangerouslySetInnerHTML={{
                __html: formatDescription(INSPIRED.description[locale]),
              }}
            />
          </div>
        </div>
      </section>

      {/* Who We Are Section */}
      <section className={sectionClassName}>
        <div
          className={
            'grid grid-cols-[auto_min(40%,506px)] gap-6 lg:grid-cols-1'
          }
        >
          <div className={'space-y-6'}>
            <Title text={WHO_WE_ARE.title[locale]} />
            <p className={`barlow`}>{WHO_WE_ARE.description[locale]}</p>
            <AppLink href={'/leadership'} text={WHO_WE_ARE.urLText[locale]} />
          </div>
          <div>
            <AppImage
              src={'/assets/images/about-image.jpg'}
              className={'mt-24 rounded-2xl lg:mt-0'}
            />
          </div>
        </div>
      </section>

      {/* Our Core Value Section */}
      <section className={sectionClassName}>
        <Title text={VALUES.title[locale]} />
        <div
          className={`mt-10 grid grid-cols-3 gap-x-12 md:grid-cols-1 md:gap-y-6`}
        >
          {VALUES.data.map((value, index) => (
            <div key={index} className={'md:grid md:grid-cols-2 md:gap-x-4'}>
              <div className={'md:order-last'}>
                <h4 className={`barlow text-24 font-bold md:text-16`}>
                  {value.title[locale]}
                </h4>
                <p className={'barlow mb-12 mt-4 md:mb-0 md:mt-4'}>
                  {value.description[locale]}
                </p>
              </div>
              <div className={'aspect-[402/280]'}>
                <AppImage
                  src={value.image}
                  className={'rounded-2xl md:rounded-lg'}
                />
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Where to Find Us Section */}
      <section className={sectionClassName}>
        <div
          className={
            'grid grid-cols-[min(40%,506px)_auto] gap-6 lg:grid-cols-1'
          }
        >
          <div className={'mt-24 pt-2 lg:order-last lg:mt-0'}>
            <AppImage
              src="/assets/images/location.jpg"
              className={'rounded-2xl'}
            />
          </div>
          <div className={'space-y-6'}>
            <Title text={FIND_US.title[locale]} />
            <div className={'space-y-4 md:space-y-2'}>
              <h4 className={'barlow text-24 font-bold md:text-16'}>
                {trans.headquarterSouthOffice}
              </h4>
              <p className={'barlow'}>
                No.26 Street 4, Linh Chieu Ward, Thu Duc City, Ho Chi Minh City,
                Vietnam
              </p>
            </div>
            <div className={'space-y-4 md:space-y-2'}>
              <h4 className={'barlow text-24 font-bold md:text-16'}>
                {trans.northOffice}
              </h4>
              <p className={'barlow'}>
                No.26 Street 4, Linh Chieu Ward, Thu Duc City, Ho Chi Minh City,
                Vietnam
              </p>
            </div>
            <div className={'space-y-4 md:space-y-2'}>
              <h4 className={'barlow text-24 font-bold md:text-16'}>
                {trans.socialNetwork}
              </h4>
              <ul className={'flex gap-x-4'}>
                {SOCIALS.map((item, index) => (
                  <li key={index}>
                    <Link href={item.url} target={'_blank'}>
                      <Icon
                        name={item.icon}
                        className={'fill-blue'}
                        size={24}
                      />
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>
    </Main>
  );
};

export default AboutUs;
