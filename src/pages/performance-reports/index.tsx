'use client';

import { useRouter } from 'next/router';
import React from 'react';

import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Quote from '@/components/Quote';
import TextGradient from '@/components/TextGradient';
import Title from '@/components/Title';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const HEADER = {
  subTitle: {
    en: 'Visualized Reports',
    vi: 'Báo cáo trực quan',
  },
  title: {
    en: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Gain'} className={'inline'} />{' '}
        {`real-time insight into center and staff performance.`}
      </h1>
    ),
    vi: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Báo cáo'} className={'inline'} />{' '}
        {`tổng quan về hiệu suất của trung tâm và nhân viên.`}
      </h1>
    ),
  },
  description: {
    en: 'Manage performance at-a-glance. Monitor staff productivity, enrollment pipeline health, and campaign sources. Plan ahead, track attendance, oversee engagement, and get nuanced insight. Save time with automated reports.',
    vi: 'Quản lý hiệu suất một cách nhanh chóng. Theo dõi năng suất của nhân viên, tình hình đăng ký và nguồn cấp dự án. Lập kế hoạch trước, theo dõi sự tham gia và nhận thông tin chi tiết. Tiết kiệm thời gian với báo cáo tự động.',
  },
};

const HIGHLIGHTS = [
  {
    id: 1,
    title: {
      en: `Identify what's impacting your bottom line`,
      vi: 'Dễ dàng xác định các mục tiêu quan trọng của trung tâm',
    },
    description: {
      en: 'Easily view which campaigns, leads, and lost opportunities are affecting your revenue. Automatically calculate your ROI (return on investment) and CLV (customer lifetime value), with less work.',
      vi: 'Dễ dàng xem chiến dịch, khách hàng tiềm năng và cơ hội bị mất nào đang ảnh hưởng đến doanh thu của trung tâm. Tự động tính toán ROI (lợi tức đầu tư) và CLV (giá trị trọn đời của khách hàng) với ít công việc hơn.',
    },
    listing: [
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Lead Source Reporting.</span> Track
            lead sources—website, search engines, direct mail, referrals, and
            more— to understand where leads are coming from, and which sources
            are most effective.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>
              Báo cáo và theo dõi nguồn phụ huynh tiềm năng.
            </span>{' '}
            Theo dõi các nguồn khách hàng tiềm năng—trang web, công cụ tìm kiếm,
            thư trực tiếp, giới thiệu, v.v.— để hiểu khách hàng tiềm năng đến từ
            đâu và nguồn nào hiệu quả nhất.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>
              Admission Campaign Reporting.
            </span>{' '}
            See which marketing campaigns are producing enrollments so you can
            double down on your best-converting campaigns.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>
              Báo cáo chiến dịch tuyển sinh.
            </span>{' '}
            Xem chiến dịch quảng cáo tuyển sinh nào đang tạo ra lượt đăng ký để
            bạn có thể tập trung vào kênh kết nối truyền thông tốt nhất cho kế
            hoạch tuyển sinh của mình.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>
              Lost Opportunities Reporting.
            </span>{' '}
            Understand why parents are not enrolling to adjust your marketing
            strategy. Plus, see where in the enrollment funnel you’re losing
            leads to create additional training or rework your efforts.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>
              Báo cáo, phân tích hồ sơ huỷ.
            </span>{' '}
            Hiểu lý do tại sao phụ huynh không đăng ký để điều chỉnh chiến lược
            truyền thông của bạn. Ngoài ra, phân tích xem bạn đang mất đi phụ
            huynh tiềm năng ở đâu trong kênh đăng ký để đào tạo bổ sung hoặc
            triển khai chiến dịch lại.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Revenue Reporting.</span>{' '}
            Effortlessly project finances for each center. Forecast enrollment
            ratios, trends, and annual profit for the upcoming year. Further,
            use report data to deliver an outstanding family experience.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>Báo cáo doanh thu.</span> Dễ dàng
            phân tích báo cáo doanh thu tài chính cho mỗi trung tâm. Dự báo tỷ
            lệ tuyển sinh, xu hướng và lợi nhuận hàng năm cho năm tới. Hơn nữa,
            sử dụng dữ liệu báo cáo để phát triển, nâng cấp trung tâm mang lại
            trải nghiệm vượt trội cho phụ huynh và học sinh.
          </>
        ),
      },
    ],
    images: ['sample.png', 'sample.png'],
  },
  {
    id: 2,
    title: {
      en: 'Forecast open seats and monitor attendance',
      vi: 'Quản lý số lượng học sinh và theo dõi tỉ lệ đi học',
    },
    description: {
      en: 'Manage attendance, wait-lists, and classroom capacities to plan for the next school year. Further, easily forecast revenue to project profitability.',
      vi: 'Quản lý điểm danh, danh sách hồ sơ học sinh chờ và số lượng lớp học để lập kế hoạch cho năm học tiếp theo. Hơn nữa, dễ dàng dự báo doanh thu đến lợi nhuận của trung tâm.',
    },
    listing: [
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Attendance Reporting.</span> Easily
            track and report attendance in real-time. Automatically evaluate
            tuition for each child based on their scheduled attendance. Ensure
            safety by knowing where students are at all times. Plus, easily
            track staff hours to monitor payroll.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>Báo cáo điểm danh.</span> Dễ dàng
            theo dõi và báo cáo sự điểm danh với thời gian thực. Tự động tính
            giá học phí cho từng trẻ dựa trên lịch đi học. Đảm bảo an toàn bằng
            cách luôn tình trạng điểm danh của học sinh. Ngoài ra, dễ dàng theo
            dõi giờ làm của nhân viên để theo dõi bảng lương.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>
              Enrollment Forecast Reporting.
            </span>{' '}
            Estimate revenue opportunities in your pipeline. Run reports on
            potential revenue to forecast profits. Calculate your return to
            optimize marketing budgets for next year. In addition, see how much
            revenue each conversion generated.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>Báo cáo dự báo tuyển sinh.</span>{' '}
            Ước tính cơ hội doanh thu cho trung tâm của bạn. Chạy báo cáo về
            doanh thu tiềm năng để dự báo lợi nhuận. Tính toán lợi nhuận của bạn
            để tối ưu hóa ngân sách truyền thông tuyển sinh cho năm tới. Ngoài
            ra, có thể xem mỗi tỷ lệ chuyển đổi và chăm sóc phụ huynh tiềm năng
            ghi danh tuyển sinh.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Capacity Planning Reporting.</span>{' '}
            Automate a Capacity Planning report to see the day-to-day
            availability within your center. This report ties together
            children’s schedules with classroom capacities. View which
            classrooms are making the most of their available capacity. Equip
            directors and decision-makers with real-time insight.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>Báo cáo hiệu suất trung tâm.</span>{' '}
            Tự động hóa báo cáo hiệu suất hoạt động để xem tình trạng sẵn sàng
            hàng ngày trong trung tâm của bạn. Báo cáo này gắn kết lịch trình
            của trẻ với hiệu suất của lớp học. Xem những lớp học nào đang tận
            dụng tối đa hiệu suất sẵn có của mình. Trang bị cho Hiệu trưởng
            thông tin chính xác nhanh chóng để ra quyết định những định hướng
            phát triển của trung tâm.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Waitlist Reporting.</span> Gain
            insight into your waitlist. View who you have on your waitlist
            across locations. Sort by age, group, priority, or waitlist date.
            Understand where you need to establish additional efforts. Plan
            classroom configurations or plan for new centers, in a few clicks.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>
              Báo cáo danh sách hồ sơ đang chờ.
            </span>{' '}
            Có được cái nhìn tổng quan về báo cáo danh sách hồ sơ đang chờ của
            trung tâm. Xem thông tin của nhưng phụ huynh và học sinh tiềm năng.
            Sắp xếp theo độ tuổi, nhóm, mức độ ưu tiên trong danh sách chờ. Lập
            kế hoạch kế hoạch để triển khai các chiến dịch tuyển sinh bổ sun cần
            thiết.
          </>
        ),
      },
    ],
    images: ['sample.png', 'sample.png'],
  },
  {
    id: 3,
    title: {
      en: 'Automate advances reporting for nuanced insight',
      vi: 'Tự động hóa báo cáo nâng cao để có được thông tin chi tiết nhiều khía cạnh',
    },
    description: {
      en: 'Get the advanced insight you need to enhance your enrollment process. Report on nutrition-related costs and subsidies. Customize reports to track campaign segments, analyze conversion success at each enrollment stage, calculate family lifetime value, and more.',
      vi: 'Quan sát thông tin tổng quan mà bạn cần để nâng cao quá trình tuyển sinh của mình. Báo cáo về chi phí và trợ cấp liên quan đến dinh dưỡng. Tùy chỉnh báo cáo để theo dõi các phân đoạn chiến dịch, phân tích thành công chuyển đổi ở từng giai đoạn tuyển sinh, tính toán giá trị lợi ích phù hợp cho gia đình và học sinh, v.v.',
    },
    listing: [
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>USDA Reporting.</span>{' '}
            Automatically maintain USDA compliance and receive reimbursement for
            food costs. Save time and maximize profits by offering nutritional
            food options that differentiate your center from competitors.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>
              Báo cáo khẩu phần dinh dưỡng.
            </span>{' '}
            Luôn luôn cập nhật và tuân thủ mục khẩu phần dinh dưỡng của BGD và
            nhận khoản hoàn trả chi phí thực phẩm. Tiết kiệm thời gian và tối đa
            hóa lợi nhuận bằng cách cung cấp các lựa chọn thực phẩm dinh dưỡng
            giúp trung tâm của bạn khác biệt với các trung tâm giáo dục khác.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Subsidy Reporting.</span> Monitor
            subsidies across locations. Easily grant the proper funds to
            families at each center. Report on subsidies to improve data
            accuracy for licensure and tax purposes. Save valuable time.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>USDA Reporting.</span>{' '}
            Automatically maintain USDA compliance and receive reimbursement for
            food costs. Save time and maximize profits by offering nutritional
            food options that differentiate your center from competitors.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Custom Reporting.</span> Easily
            build custom reports, like tracking opens for a particular email
            campaign. Gain in-depth insight by simply customizing the enrollment
            reports you need. Plus, automate report emails to share findings and
            discuss strategy improvements.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>Báo cáo tùy chỉnh.</span> Dễ dàng
            tạo báo cáo tùy chỉnh, chẳng hạn như theo dõi mở cho một chiến dịch
            email cụ thể. Có được thông tin báo cáo chi tiết bằng cách tùy chỉnh
            các báo cáo tuyển sinh mà bạn cần. Ngoài ra, hãy tự động hóa email
            báo cáo để chia sẻ kết quả và thảo luận về các cải tiến chiến lược.
          </>
        ),
      },
    ],
    images: ['sample.png', 'sample.png'],
  },
  {
    id: 4,
    title: {
      en: 'Monitor engagement levels',
      vi: 'Theo dõi mức độ tương tác của nhân viên',
    },
    description: {
      en: 'Record staff hours, simplify payroll and licensing, and monitorengagement with less work. Ensure staff is consistently interacting with families. In addition, track family engagement to increase satisfaction and build loyal customers.',
      vi: 'Ghi lại giờ làm của nhân viên, đơn giản hóa việc trả lương và cấp phép, đồng thời giám sát nhân viên với ít công việc thủ công hơn. Đảm bảo nhân viên luôn tương tác với gia đình. Ngoài ra, hãy theo dõi sự tương tác của gia đình để tăng sự hài lòng và xây dựng niềm tin từ phụ huynh.',
    },
    listing: [
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Staff Reporting.</span> Access and
            review staff hours. Keep staff licensure up to date and ensure each
            staff member’s education certification is renewed. Plus, monitor
            employee activity to see how families interact with the messages
            they receive from your center.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>Báo cáo nhân sự.</span> Truy cập và
            xem xét giờ làm việc của nhân viên. Luôn cập nhật giấy phép nhân
            viên và đảm bảo chứng chỉ giáo dục của mỗi nhân viên được gia hạn.
            Ngoài ra, hãy theo dõi hoạt động của nhân viên để xem cách các gia
            đình tương tác với những tin nhắn họ nhận được từ trung tâm của bạn.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Family Engagement Reporting.</span>{' '}
            Receive real-time feedback on the milestones and photos your center
            shares via a digital newsfeed. Parents can comment or react to media
            updates. That way, they can inform teachers of the impact they’re
            making each day. Monitor activity with reports that detail
            engagement rates for each family.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>
              Báo cáo sự hài lòng của gia đình.
            </span>{' '}
            Nhận phản hồi theo thời gian thực về các cột mốc quan trọng và hình
            ảnh mà trung tâm của bạn chia sẻ thông qua nguồn cấp tin tức trực
            tuyến. Phụ huynh có thể bình luận hoặc phản ứng với các cập nhật
            trên phương tiện truyền thông. Bằng cách đó, họ có thể thông báo cho
            giáo viên về tác động mà họ đang tạo ra mỗi ngày. Giám sát hoạt động
            bằng các báo cáo chi tiết về tỷ lệ tưởng tác của từng phụ huynh.
          </>
        ),
      },
    ],
    images: ['sample.png', 'sample.png'],
  },
];

const QUOTE = {
  en: 'Optimize your staff and parent experience with real-time insight',
  vi: 'Tối ưu hóa trải nghiệm của nhân viên và phụ huynh với việc truyền thông tin theo thời gian thực',
};

const PerformanceReports = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <Main meta={<Meta title={trans.performanceReports} description={''} />}>
      <section className={`${sectionClassName} pt-0`}>
        <div className={'grid grid-cols-2 lg:grid-cols-1 lg:gap-y-2'}>
          <div className={'space-y-10 md:space-y-6'}>
            <h5 className={`barlow mb-10 text-18 text-red underline`}>
              <b>{HEADER.subTitle[locale]}</b>
            </h5>
            {HEADER.title[locale]()}
            <p className={'mb-14 mt-10 max-w-[506px]'}>
              {HEADER.description[locale]}
            </p>
            <div
              className={'flex items-center gap-8 lg:flex-col lg:items-start'}
            >
              <AppLink href={'/get-quote'} text={trans.requestADemo} />
              <AppLink
                href={'/principals-management'}
                text={trans.seeItInAction}
              />
            </div>
          </div>
          <AppImage
            src="/assets/images/performance-reports/header.png"
            className={'!object-contain lg:mx-auto lg:max-w-[480px] md:hidden'}
          />
        </div>
      </section>

      <section className={sectionClassName}>
        <div className={'space-y-[112px] md:space-y-12'}>
          {HIGHLIGHTS.map((box, index) => (
            <div key={index}>
              <div
                className={'max-w-[70%] space-y-6 lg:max-w-full md:space-y-4'}
              >
                <Title text={box.title[locale]} />
                <p className={'barlow'}>{box.description[locale]}</p>
              </div>
              <div
                className={'mb-12 mt-6 grid grid-cols-4 gap-6 lg:grid-cols-2'}
              >
                {box.listing.map((item, i) => (
                  <p key={i} className={'barlow'}>
                    {item[locale]()}
                  </p>
                ))}
              </div>
              <div className={'grid grid-cols-2 gap-x-6 md:gap-x-4'}>
                {box.images.map((img, idx) => (
                  <div key={idx} className={'aspect-[640/400]'}>
                    <AppImage
                      src={`/assets/images/performance-reports/${img}`}
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      <section className={`${sectionClassName} max-w-full bg-blue`}>
        <h2
          className={
            'barlow--bold mx-auto max-w-[1048px] px-4 text-center text-48 text-yellow  lg:text-32 md:py-3 md:text-24'
          }
        >
          {QUOTE[locale]}
        </h2>
      </section>

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export default PerformanceReports;
