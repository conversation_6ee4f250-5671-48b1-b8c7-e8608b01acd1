'use client';

import { useRouter } from 'next/router';
import React from 'react';

import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Button from '@/components/Button';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import TextGradient from '@/components/TextGradient';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const HEADER = {
  title: {
    en: 'Parent engagement app',
    vi: 'Ứng dụng tương tác dành cho Phụ huynh',
  },
  description: {
    en: 'The Mầm Chồi Lá Parent App is a tool that connects parents and schools anytime, anywhere. With standout features such as real-time updates on your child’s activities, menu, and health, easy communication with teachers via chat and instant notifications, as well as management of tuition fees, BMI reports, and schedules—all in one app. Download now to accompany your beloved child on their growth journey in a modern and convenient way! 🌟📲',
    vi: '<PERSON><PERSON><PERSON>rent App là công cụ kết nối phụ huynh và nhà trường mọi lúc, mọi nơi. Với các tính năng nổi bật như cập nhật tức thì hoạt động, thực đơn và sức khỏe của bé, giao tiếp dễ dàng với giáo viên qua chat và thông báo tức thời, cùng quản lý học phí, báo cáo BMI và lịch trình chỉ trong một ứng dụng. Hãy tải ngay để đồng hành cùng con yêu trên hành trình phát triển một cách hiện đại và tiện lợi! 🌟📲',
  },
  hey: {
    en: 'Hey, Parent!',
    vi: 'Xin chào, Phụ huynh!',
  },
  thisAppIsForYou: {
    en: 'this app is for you',
    vi: 'cùng tìm hiểu nhé.',
  },
};

const HIGHLIGHT = [
  {
    title: {
      en: 'Daily attendance information',
      vi: 'Thông tin điểm danh mỗi ngày',
    },
    description: {
      en: () => (
        <>
          Helps parents stay updated on their child’s daily attendance with{' '}
          <b>just one tap</b>. 🚸 Receive instant notifications when your child
          arrives at or leaves school, backed by the security of{' '}
          <b>AI-powered facial recognition cameras</b>, ensuring your peace of
          mind about your child’s safety. This feature not only enhances
          transparency but also strengthens the connection between families and
          schools. 📲✨
        </>
      ),
      vi: () => (
        <>
          Giúp phụ huynh cập nhật tình hình điểm danh của bé mỗi ngày{' '}
          <b>chỉ trong một chạm</b>. 🚸 Nhận thông báo tức thì khi bé đến hoặc
          rời trường với sự đảm bảo của <b>Camera AI nhận diện khuôn mặt</b>,
          đảm bảo bạn luôn an tâm về sự an toàn của con. Tính năng này không chỉ
          minh bạch mà còn giúp tăng cường kết nối giữa gia đình và nhà trường.
          📲✨
        </>
      ),
    },
    images: [
      '/assets/images/parents-application/check-in.png',
      '/assets/images/parents-application/leave-history.png',
    ],
  },
  {
    title: {
      en: 'Daily activity reports',
      vi: 'Báo cáo hoạt động hằng ngày',
    },
    description: {
      en: () => (
        <>
          {`Provides parents with a comprehensive overview of their child's day at
          school. 🌟 From learning activities and playtime to meals and naps,
          every detail is updated clearly and transparently. With just one tap,
          you can accompany and understand your child’s development journey
          every single day! 📲💕`}
        </>
      ),
      vi: () => (
        <>
          {`Mang đến cho phụ huynh cái nhìn toàn diện về một ngày của bé tại
          trường. 🌟 Từ các hoạt động học tập, vui chơi, đến bữa ăn và giấc ngủ,
          mọi thông tin đều được cập nhật chi tiết và minh bạch. Chỉ cần một
          chạm, bạn đã có thể đồng hành và thấu hiểu hành trình phát triển của
          con yêu mỗi ngày! 📲💕`}
        </>
      ),
    },
    images: [
      '/assets/images/parents-application/daily-report-1.png',
      '/assets/images/parents-application/daily-report-2.png',
    ],
  },
  {
    title: {
      en: 'Important announcements',
      vi: 'Các thông báo quan trọng',
    },
    description: {
      en: () => (
        <>
          {`A fast and convenient information channel for parents. 📝 Instantly
          receive important updates from the school, such as event schedules,
          timetable changes, or urgent news. All information is displayed
          clearly, ensuring you never miss anything important in your child’s
          educational journey! 📲✨`}
        </>
      ),
      vi: () => (
        <>
          {`Là kênh thông tin nhanh chóng và tiện lợi dành cho phụ huynh. 📝 Nhận
          ngay các thông báo quan trọng từ nhà trường như lịch sự kiện, thay đổi
          thời khóa biểu, hoặc tin tức khẩn cấp. Tất cả thông tin đều hiển thị
          rõ ràng, giúp bạn không bao giờ bỏ lỡ những điều quan trọng trong hành
          trình học tập của con! 📲✨`}
        </>
      ),
    },
    images: [
      '/assets/images/parents-application/notify-1.png',
      '/assets/images/parents-application/notify-2.png',
    ],
  },
  {
    title: {
      en: 'Tuition & payment history',
      vi: 'Học phí & Lịch sử thanh toán',
    },
    description: {
      en: () => (
        <>
          {`Helps parents easily manage tuition payments automatically and
          transparently. 💳 Receive payment reminders and check transaction
          history anytime, anywhere, with just one tap. This feature not only
          saves time but also ensures parents always stay informed about their
          child’s financial matters at school. 📲✨`}
        </>
      ),
      vi: () => (
        <>
          {`Giúp phụ huynh dễ dàng quản lý học phí một cách tự động và minh bạch.
          💳 Nhận thông báo nhắc nhở thanh toán, kiểm tra lịch sử giao dịch mọi
          lúc, mọi nơi chỉ trong một chạm. Tính năng này không chỉ tiết kiệm
          thời gian mà còn đảm bảo phụ huynh luôn nắm rõ tình hình tài chính của
          bé tại trường. 📲✨`}
        </>
      ),
    },
    images: [
      '/assets/images/parents-application/tuition.png',
      '/assets/images/parents-application/transaction.png',
    ],
  },
  {
    title: {
      en: 'Physical health information',
      vi: 'Thông tin sức khoẻ thể chất',
    },
    description: {
      en: () => (
        <>
          {`Provides parents with a clear and detailed view of their child’s
          health. 🩺 Each month, you’ll receive a BMI report and other important
          health information, allowing you to monitor your child’s development
          scientifically and accurately. This is a valuable tool for parents and
          schools to work together in providing the best care for your little
          one! 📲✨`}
        </>
      ),
      vi: () => (
        <>
          {`Mang đến cho phụ huynh cái nhìn rõ ràng và chi tiết về tình hình sức
          khỏe của bé. 🩺 Mỗi tháng, bạn sẽ nhận được báo cáo chỉ số BMI và các
          thông tin sức khỏe quan trọng, giúp theo dõi sự phát triển của con một
          cách khoa học và chính xác. Đây là công cụ hữu ích để phụ huynh và nhà
          trường phối hợp chăm sóc bé yêu tốt nhất! 📲✨`}
        </>
      ),
    },
    images: [
      '/assets/images/parents-application/bmi.png',
      '/assets/images/parents-application/health-checkup.png',
    ],
  },
  {
    title: {
      en: 'Chat channel with the school',
      vi: 'Kênh chat với Nhà trường',
    },
    description: {
      en: () => (
        <>
          {`Enables parents to connect directly with teachers, staff, or school
          departments with just one tap. 💬 Send and receive messages quickly,
          get instant answers to your questions, and have all information stored
          transparently and securely. This feature ensures convenient
          communication, fostering a strong partnership between parents and
          schools in supporting your child’s development journey! 📲✨`}
        </>
      ),
      vi: () => (
        <>
          {`Giúp phụ huynh kết nối trực tiếp với giáo viên, nhân viên vận hành,
          hoặc các phòng ban trong trường chỉ với một chạm. 💬 Gửi và nhận tin
          nhắn nhanh chóng, giải đáp thắc mắc tức thì, mọi thông tin đều được
          lưu trữ minh bạch và bảo mật. Tính năng này đảm bảo sự liên lạc thuận
          tiện, giúp phụ huynh và nhà trường luôn đồng hành chặt chẽ trong hành
          trình phát triển của bé! 📲✨`}
        </>
      ),
    },
    images: [
      '/assets/images/parents-application/class-info.png',
      '/assets/images/parents-application/chat.png',
    ],
  },
];

const DOWNLOAD_APP = {
  title: {
    vi: 'Tải Ứng dụng Phụ huynh tại đây',
    en: 'Download the Parent App here',
  },
  description: {
    vi: 'Vui lòng quét mã để tải Ứng dụng Phụ huynh và bắt đầu khám phá hành trình học tập tuyệt vời của bé!',
    en: "Please scan the code to download the Parent App and start exploring your child's amazing learning journey!",
  },
};

const ParentAppDownload = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <Main meta={<Meta title={trans.parentsApplication} description={''} />}>
      {/* Header Page Section */}
      <section className={sectionClassName}>
        <div className={'grid grid-cols-[50%_auto] gap-x-4 lg:grid-cols-1'}>
          <div className={'space-y-10 md:space-y-6'}>
            <h5 className={`barlow text-18 text-red underline md:text-14`}>
              <b>{HEADER.title[locale]}</b>
            </h5>
            <div>
              <TextGradient
                text={HEADER.hey[locale]}
                className={'whitespace-nowrap'}
              />
              <p className={'whitespace-nowrap text-72 lg:text-48 md:text-40'}>
                {HEADER.thisAppIsForYou[locale]}
              </p>
            </div>
            <p>{HEADER.description[locale]}</p>
            <div
              className={'flex items-center gap-8 lg:flex-col lg:items-start'}
            >
              <Button
                className={'!rounded-[30px] bg-white'}
                url={'https://mamchoila.onelink.me/LjvQ/q2lz1l7v?af_qr=true'}
              >
                <>
                  <span className={'text-dark'}>{trans.downloadParentApp}</span>
                  <Icon name={'arrow-down-2'} className={'fill-dark'} />
                </>
              </Button>
              <AppLink href={'/'} text={trans.requestADemo} />
            </div>
          </div>
          <div>
            <AppImage
              src="/assets/images/parent-app-head-banner.png"
              className={'!object-contain md:mt-8'}
            />
          </div>
        </div>
      </section>

      <section className={sectionClassName}>
        <div
          className={
            'grid grid-cols-3 justify-between gap-12 md:grid-cols-1 md:gap-10'
          }
        >
          {HIGHLIGHT.map((item, index) => (
            <div
              className={'flex flex-col items-center text-center'}
              key={index}
            >
              <div className={'flex gap-x-2'}>
                {item.images.map((img, idx) => (
                  <div className={'w-[170px] md:w-[130px]'} key={idx}>
                    <AppImage src={img} />
                  </div>
                ))}
              </div>
              <h3 className={'mb-4 mt-6 text-18 text-blue md:text-16'}>
                {item.title[locale]}
              </h3>
              <p>{item.description[locale]()}</p>
            </div>
          ))}
        </div>
      </section>

      <section className={'bg-blue'}>
        <div
          className={
            'relative mx-auto grid max-w-[1336px] grid-cols-[auto_520px] px-4 md:grid-cols-1'
          }
        >
          <div className={'py-21 md:py-12 md:text-center'}>
            <h2 className={'text-48 text-yellow md:text-32'}>
              {DOWNLOAD_APP.title[locale]}
            </h2>
            <p className={'mb-8 mt-10 w-[60%] text-white md:mt-6 md:w-full'}>
              {DOWNLOAD_APP.description[locale]}
            </p>
            <div className={'w-[160px] md:hidden'}>
              <AppImage
                src={'/assets/images/parents-application/qr-code.png'}
              />
            </div>
            <Button
              className={'mx-auto hidden !rounded-[30px] bg-white md:flex'}
              url={'https://mamchoila.onelink.me/LjvQ/q2lz1l7v?af_qr=true'}
            >
              <>
                <span className={'text-dark'}>{trans.downloadParentApp}</span>
                <Icon name={'arrow-down-2'} className={'fill-dark'} />
              </>
            </Button>
          </div>
          <div
            className={'absolute -top-10 right-0 w-[510px] md:static md:w-full'}
          >
            <AppImage src={'/assets/images/parents-application/app.png'} />
          </div>
        </div>
      </section>

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export default ParentAppDownload;
