import Document, { Head, Html, Main, NextScript } from 'next/document';
import Script from 'next/script';

import { AppConfig } from '@/utils/AppConfig';

// Need to create a custom _document because i18n support is not compatible with `next export`.
class MyDocument extends Document {
  // eslint-disable-next-line class-methods-use-this
  render() {
    return (
      <Html lang={AppConfig.locale}>
        <Head />
        <body>
          {/* <!-- Messenger Chat plugin Code --> */}
          <div id="fb-root"></div>

          {/* <!-- Your Chat plugin code --> */}
          <div id="fb-customer-chat" className="fb-customerchat"></div>
          <Script
            id="messenger-tag"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                var chatbox = document.getElementById('fb-customer-chat');
                chatbox.setAttribute("page_id", "154972624356438");
                chatbox.setAttribute("attribution", "biz_inbox");
              `,
            }}
          ></Script>
          <Script
            id="messenger-sdk"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                window.fbAsyncInit = function() {
                FB.init({
                  xfbml            : true,
                  version          : 'v18.0'
                });
              };
        
              (function(d, s, id) {
                var js, fjs = d.getElementsByTagName(s)[0];
                if (d.getElementById(id)) return;
                js = d.createElement(s); js.id = id;
                js.src = 'https://connect.facebook.net/vi_VN/sdk/xfbml.customerchat.js';
                fjs.parentNode.insertBefore(js, fjs);
              }(document, 'script', 'facebook-jssdk'));
              `,
            }}
          ></Script>
          <Main />
          <NextScript />
        </body>
      </Html>
    );
  }
}

export default MyDocument;
