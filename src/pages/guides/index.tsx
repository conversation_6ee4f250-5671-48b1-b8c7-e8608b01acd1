'use client';

import React from 'react';

import NineBlog from '@/components/9Blogs';
import BlogFilter from '@/components/BlogFilter';
import Quote from '@/components/Quote';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';

const Guide = () => {
  return (
    <Main meta={<Meta title={''} description={''} />}>
      <div
        className={`mx-auto flex h-full min-h-[820px] flex-col justify-between pt-[120px]`}
      >
        {/* Header Page Section */}
        <div className={`mb-[64px] px-24 pt-[120px]`}>
          <div
            className={`mx-auto mb-[40px] flex h-full w-full max-w-[1728px] flex-col justify-center px-24 text-left`}
          >
            <p
              className={`mb-[24px] bg-gradient-to-tr from-[#EE9CA7] to-[#FFDDE1] bg-clip-text text-[72px] font-bold text-transparent`}
            >
              Need help!
              <br />
              <span className={`font-normal text-[#58595B]`}>
                read our latest guidelines for you.
              </span>
            </p>
          </div>
        </div>

        {/* Blog Search & Filter Section */}
        <BlogFilter />

        {/* 9 Blog Thumbnails Section */}
        <NineBlog />

        {/* Quote Section */}
        <Quote />
      </div>
    </Main>
  );
};

export default Guide;
