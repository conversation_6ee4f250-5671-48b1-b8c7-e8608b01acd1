'use client';

import { useRouter } from 'next/router';
import React from 'react';

import AppImage from '@/components/AppImage';
import Button from '@/components/Button';
import CustomerSupport from '@/components/Customer';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import TextGradient from '@/components/TextGradient';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const PACKAGE_TYPES = [
  {
    id: 1,
    text: {
      en: 'Price according to number of students',
      vi: '<PERSON>i<PERSON> theo số lượng học sinh',
    },
  },
  {
    id: 2,
    text: {
      en: 'Price Per Package',
      vi: 'Giá theo gói',
    },
  },
];

const PACKAGES = [
  {
    id: 1,
    name: 'basic',
    students: '40',
    pricePerStudent: '14.000',
    action: {
      text: 'tryItNow',
      shortText: 'try',
      url: '/get-quote',
    },
    image: 'goi-co-ban.png',
    features: [
      {
        text: 'Website Trường học',
        enable: true,
      },
      {
        text: 'Quản lý nội dung Website',
        enable: true,
      },
      {
        text: 'Tuỳ chỉnh SEO Website',
        enable: true,
      },
      {
        text: 'Đơn đăng ký tuyển sinh trực tuyến',
        enable: true,
      },
      {
        text: 'Quản lý Tuyển sinh',
        enable: true,
      },
      {
        text: 'Quản lý Học sinh  & Phụ Huynh',
        enable: true,
      },
      {
        text: 'Cấu hình tuỳ chỉnh Học phí',
        enable: false,
      },
      {
        text: 'Quản lý Học phí tự động',
        enable: false,
      },
      {
        text: 'Điểm danh với Camera AI',
        enable: false,
      },
      {
        text: 'Quản lý nhân viên',
        enable: true,
      },
      {
        text: 'Cấu hình tuỳ chỉnh Lương',
        enable: false,
      },
      {
        text: 'Quản lý Lương thưởng tự động',
        enable: false,
      },
      {
        text: 'Tin nhắn Zalo thông báo tự động',
        enable: false,
      },
      {
        text: 'Cổng thông tin trực tuyến cho Phụ huynh',
        enable: false,
      },
      {
        text: 'Báo cáo tài chính tự động',
        enable: false,
      },
    ],
    description: {
      vi: 'Phù hợp với nhóm trẻ  quy mô nhỏ',
      en: 'Suitable for small-scale youth groups.'
    },
  },
  {
    id: 2,
    name: 'advanced',
    students: '100',
    pricePerStudent: '24.000',
    image: 'goi-nang-cao.png',
    action: {
      text: 'getAQuote',
      shortText: 'quote',
      url: '/get-quote',
    },
    features: [
      {
        text: 'Website Trường học',
        enable: true,
      },
      {
        text: 'Quản lý nội dung Website',
        enable: true,
      },
      {
        text: 'Tuỳ chỉnh SEO Website',
        enable: true,
      },
      {
        text: 'Đơn đăng ký tuyển sinh trực tuyến',
        enable: true,
      },
      {
        text: 'Quản lý Tuyển sinh',
        enable: true,
      },
      {
        text: 'Quản lý Học sinh  & Phụ Huynh',
        enable: true,
      },
      {
        text: 'Cấu hình tuỳ chỉnh Học phí',
        enable: true,
      },
      {
        text: 'Quản lý Học phí tự động',
        enable: true,
      },
      {
        text: 'Điểm danh với Camera AI',
        enable: false,
      },
      {
        text: 'Quản lý nhân viên',
        enable: true,
      },
      {
        text: 'Cấu hình tuỳ chỉnh Lương',
        enable: true,
      },
      {
        text: 'Quản lý Lương thưởng tự động',
        enable: true,
      },
      {
        text: 'Tin nhắn Zalo thông báo tự động',
        enable: false,
      },
      {
        text: 'Cổng thông tin trực tuyến cho Phụ huynh',
        enable: true,
      },
      {
        text: 'Báo cáo tài chính tự động',
        enable: true,
      },
    ],
    description: {
      vi: 'Phù hợp với lớp mẫu giáo quy mô vừa',
      en: 'Suitable for a medium-sized kindergarten class.'
    },
  },
  {
    id: 3,
    name: 'professional',
    students: '180',
    pricePerStudent: '34.000',
    image: 'goi-chuyen-nghiep.png',
    action: {
      text: 'getAQuote',
      shortText: 'quote',
      url: '/get-quote',
    },
    features: [
      {
        text: 'Website Trường học',
        enable: true,
      },
      {
        text: 'Quản lý nội dung Website',
        enable: true,
      },
      {
        text: 'Tuỳ chỉnh SEO Website',
        enable: true,
      },
      {
        text: 'Đơn đăng ký tuyển sinh trực tuyến',
        enable: true,
      },
      {
        text: 'Quản lý Tuyển sinh',
        enable: true,
      },
      {
        text: 'Quản lý Học sinh  & Phụ Huynh',
        enable: true,
      },
      {
        text: 'Cấu hình tuỳ chỉnh Học phí',
        enable: true,
      },
      {
        text: 'Quản lý Học phí tự động',
        enable: true,
      },
      {
        text: 'Điểm danh với Camera AI',
        enable: true,
      },
      {
        text: 'Quản lý nhân viên',
        enable: true,
      },
      {
        text: 'Cấu hình tuỳ chỉnh Lương',
        enable: true,
      },
      {
        text: 'Quản lý Lương thưởng tự động',
        enable: true,
      },
      {
        text: 'Tin nhắn Zalo thông báo tự động',
        enable: true,
      },
      {
        text: 'Cổng thông tin trực tuyến cho Phụ huynh',
        enable: true,
      },
      {
        text: 'Báo cáo tài chính tự động',
        enable: true,
      },
    ],
    description: {
      vi: 'Phù hợp với trường mầm non quy mô vừa và lớn',
      en: 'Suitable for medium and large-scale preschools.'
    },
  },
];

const CLIENTS = [
  '/assets/images/clients/phuong-hoang.jpg',
  '/assets/images/clients/nang-hong.jpg',
];

const COMPARE_PLANS = {
  essentials: [
    {
      icon: 'frame',
      title: 'maximumStudents',
      values: [40, 100, 180],
    },
    {
      icon: 'user-circle-add',
      title: 'addMoreStudents',
      values: ['10.000', '20.000', '30.000'],
    },
  ],
  otherData: [
    {
      title: 'onlineContentManagement',
      data: [
        {
          icon: 'monitor',
          title: 'schoolLandingPageWebsite',
          values: [true, true, true],
        },
        {
          icon: 'global-edit',
          title: 'websiteContentManagement',
          values: [true, true, true],
        },
        {
          icon: 'profile-add',
          title: 'enrollmentOnlineForm',
          values: [true, true, true],
        },
        {
          icon: 'document-text',
          title: 'enrollmentOnlineFormManagement',
          values: [true, true, true],
        },
      ],
    },
    {
      title: 'engagementOperationApplications',
      data: [
        {
          icon: 'mobile',
          title: 'teachersNanniesLeadingMobileApp',
          values: [false, true, true],
        },
        {
          icon: 'setting-2',
          title: 'principalsOperationManagementSystem',
          values: [true, true, true],
        },
      ],
    },
    {
      title: 'studentModuleManagementSystems',
      data: [
        {
          icon: 'frame',
          title: 'studentsManagement',
          values: [true, true, true],
        },
        {
          icon: 'profile-2user',
          title: 'parentsManagement',
          values: [true, true, true],
        },
        {
          icon: 'people',
          title: 'enrollmentsManagement',
          values: [true, true, true],
        },
        {
          icon: 'receipt',
          title: 'tuitionsManagement',
          values: [false, true, true],
        },
      ],
    },
    {
      title: 'schoolModuleManagementSystems',
      data: [
        {
          icon: '3dcube',
          title: 'classesManagement',
          values: [true, true, true],
        },
        {
          icon: 'okb-(okb)',
          title: 'schoolEventsManagement',
          values: [false, true, true],
        },
        {
          icon: 'login',
          title: 'checkedInManagement',
          values: [false, true, true],
        },
        {
          icon: 'login',
          title: 'manageAutomaticNotifications',
          values: [false, false, true],
        },
        {
          icon: 'flag',
          title: 'leaveRequestsManagement',
          values: [false, true, true],
        },
        {
          icon: 'graph',
          title: 'visualizedDashboard',
          values: [false, true, true],
        },
      ],
    },
    {
      title: 'staffModuleManagementSystems',
      data: [
        {
          icon: 'briefcase',
          title: 'staffsManagement',
          values: [false, true, true],
        },
        {
          icon: 'receipt-text',
          title: 'payrollsManagement',
          values: [false, true, true],
        },
      ],
    },
    {
      title: 'communicationModuleManagementSystems',
      data: [
        {
          icon: 'slider-vertical',
          title: 'bannersManagement',
          values: [false, true, true],
        },
        {
          icon: 'reserve',
          title: 'manageDailyMenu',
          values: [false, true, true],
        },
        {
          icon: 'discount-shape',
          title: 'manageWeeklySchedule',
          values: [false, true, true],
        },
        {
          icon: 'notification',
          title: 'manageBMI',
          values: [false, true, true],
        },
      ],
    },
    {
      title: 'integrationIoTSmartDevices',
      data: [
        {
          icon: 'monitor-recorder',
          title: 'aiCheckInCameras',
          values: [false, false, true],
        },
        {
          icon: 'monitor-recorder',
          title: 'zaloZNSAutomaticMessages',
          values: [false, false, true],
        },
        {
          icon: 'card-pos',
          title: 'onlinePayment',
          values: [false, true, true],
        },
        {
          icon: 'directbox-notif',
          title: 'billPrinters',
          values: [true, true, true],
        },
      ],
    },
    {
      title: 'support',
      data: [
        {
          icon: 'coffee',
          title: 'saleSupports',
          values: [true, true, true],
        },
        {
          icon: 'flash',
          title: 'consumerSupport247',
          values: [false, true, true],
        },
      ],
    },
  ],
};

const HEADER = {
  title: {
    en: () => (
      <>
        We <TextGradient text={'Flexible'} className={'inline'} /> for every
        budget.
      </>
    ),
    vi: () => (
      <>
        Mầm Chồi Lá <TextGradient text={'linh hoạt'} className={'inline'} /> cho
        mọi ngân sách.
      </>
    ),
  },
  description: {
    en: () => (
      <>
        We are making education that values honestly, integrity and efficiency.
        <b> Building quality products and caring.</b>
      </>
    ),
    vi: () => (
      <>
        Mầm Chồi Lá hổ trợ xây dựng nền tảng giáo dục tập trung vào sự chuẩn
        xác, minh bạch và hiệu quả.
        <b> Xây dựng hệ thống quản lý chất lượng và chu đáo.</b>
      </>
    ),
  },
};

// const FEATURE_EXTRA = {
//   seed: {
//     en: () => (
//       <>
//         All <b>Starter</b> Plan Features
//       </>
//     ),
//     vi: () => (
//       <>
//         Toàn bộ tính năng gói <b>Hạt</b>
//       </>
//     ),
//   },
//   bud: {
//     en: () => (
//       <>
//         All <b>Seed</b> Plan Features
//       </>
//     ),
//     vi: () => (
//       <>
//         Toàn bộ tính năng gói <b>Mầm</b>
//       </>
//     ),
//   },
// };

const Pricing = () => {
  const trans: any = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <Main meta={<Meta title={trans.pricing} description={''} />}>
      <section className={`${sectionClassName} max-w-[1600px]`}>
        <div className={'text-center md:text-left'}>
          <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
            {HEADER.title[locale]()}
          </h1>
          <p className={'mx-auto mb-12 mt-4 max-w-[720px] md:mb-8'}>
            {HEADER.description[locale]()}
          </p>
        </div>
        <div className={'flex justify-center gap-x-6 md:flex-col md:items-center md:gap-4'}>
          {PACKAGE_TYPES.map((item) => {
            const isSelected = item.id === 1;
            return (
              <Button
                key={item.id}
                outline={!isSelected}
                className={`!rounded-[32px]`}
              >
                <>
                  {item.text[locale]}
                  {item.id === 2 && (
                    <p
                      className={
                        'barlow--bold rounded-sm bg-primaryGradient px-3 py-1 text-12 text-white'
                      }
                    >
                      {locale === 'vi' ? 'Giảm 10%' : '10% OFF'}
                    </p>
                  )}
                </>
              </Button>
            );
          })}
        </div>
        <div
          className={
            'mt-20 grid grid-cols-3 gap-6 lg:mt-8 md:grid-cols-1'
          }
        >
          {PACKAGES.map((item) => {
            const { students, pricePerStudent, action, features, image } =
              item || {};
            return (
              <div
                key={item.id}
                className={`cursor-pointer space-y-10 rounded-2xl border-[12px] border-white bg-white p-8 shadow-normal transition-all duration-100 lg:self-auto md:space-y-6 md:p-6 ${
                  item.id === 3 ? '!border-primary/30' : ''
                }`}
              >
                <div className={'flex items-center justify-between gap-x-4'}>
                  <h2 className={'text-48 md:text-24'}>{trans[item.name]}</h2>
                  {item.id === 1 && (
                    <p className={'text-14'}>
                      {locale === 'vi' ? (
                        <>
                          <b className={'text-32 text-red md:text-18'}>16.000</b><b>VNĐ</b>
                          /tháng
                        </>
                      ) : (
                        <>
                          <b className={'text-32 text-red md:text-18'}>$0.66</b>
                          /month
                        </>
                      )}
                    </p>
                  )}
                  {item.id === 3 && (
                    <Icon name={'verify'} className={'fill-red'} size={40} />
                  )}
                </div>
                <div className={'flex items-center justify-between gap-x-2'}>
                  <p className={'text-14 text-blue'}>
                    <b>{students}</b> {trans.students}
                  </p>
                  <p className={'text-14'}>
                    <b className={'text-red'}>+{pricePerStudent}</b> <b>VNĐ</b> / {trans.extraStudents}
                  </p>
                </div>
                <div>
                  <AppImage src={`/assets/images/${image}`} className={'h-[180px] w-auto !object-contain'} />
                </div>
                <Button
                  url={action.url}
                  className={'!rounded-[32px] !bg-yellow md:w-full'}
                >
                  <>
                    <span className={'text-16 text-dark'}>
                      {/* @ts-ignore */}
                      {trans[action.text]}
                    </span>
                    <Icon name={'export-circle'} className={'fill-dark'} />
                  </>
                </Button>
                <div className={'space-y-4'}>
                  <p>{trans.features}:</p>
                  <ul className={'space-y-4'}>
                    {features.map((feature, index) => (
                      <li key={index} className={feature.enable ? '' : 'line-through opacity-30'}>
                        <b>{feature.text}</b>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            );
          })}
        </div>

        <div className={'mt-6 grid grid-cols-2 gap-6 md:grid-cols-1'}>
          <div className={'space-y-8 rounded-2xl bg-white p-12 md:space-y-4 md:p-6'}>
            <h2 className={'text-48 md:text-center md:text-24'}>
              <b>Điểm danh thông minh với Camera AI</b>
            </h2>
            <div className={'w-[144px] md:mx-auto'}>
              <AppImage  src={'/assets/images/camera.png'} />
            </div>
            <Button
              url={'/get-quote'}
              className={'!rounded-[32px] !bg-yellow md:w-full'}
            >
              <>
                <span className={'text-16 text-dark'}>
                  {trans.tryItNow}
                </span>
                <Icon name={'export-circle'} className={'fill-dark'} />
              </>
            </Button>
          </div>

          <div className={'space-y-8 rounded-2xl bg-white p-12 md:space-y-4 md:p-6'}>
            <h2 className={'text-48 md:text-center md:text-24'}>
              <b>Tin nhắn thông báo tự động qua Zalo</b>
            </h2>
            <div className={'w-[256px] md:mx-auto'}>
              <AppImage src={'/assets/images/zalo.png'} />
            </div>
            <Button
              url={'/get-quote'}
              className={'!rounded-[32px] !bg-yellow md:w-full'}
            >
              <>
                <span className={'text-16 text-dark'}>
                  {trans.tryItNow}
                </span>
                <Icon name={'export-circle'} className={'fill-dark'} />
              </>
            </Button>
          </div>
        </div>
      </section>

      <section
        className={`${sectionClassName} max-w-[1700px] space-y-16 md:space-y-6`}
      >
        <p className={'text-center text-24 md:text-left md:text-18'}>
          {locale === 'vi'
            ? 'Các trung tâm giáo dục đang hài lòng với hệ thống quản lý toàn diện Mầm Chồi Lá'
            : 'Our happy daycare centers with completely unified platform'}
        </p>
        <div
          className={`flex items-center justify-center gap-x-16 md:grid md:grid-cols-2 md:gap-x-8`}
        >
          {CLIENTS.map((image, index) => (
            <div className={'h-[160px] lg:h-auto'} key={index}>
              <AppImage src={image} />
            </div>
          ))}
        </div>
      </section>

      {/* <FaqListing className={'!mt-0'} /> */}

      <section className={`${sectionClassName} max-w-[1600px] md:px-0`}>
        <h1
          className={
            'text-center text-72 font-normal lg:text-48 md:px-4 md:text-left md:text-40'
          }
        >
          {locale === 'vi' ? (
            <>
              <TextGradient text={'So sánh'} className={'inline'} /> tất cả các
              gói dịch vụ.
            </>
          ) : (
            <>
              <TextGradient text={'Compare'} className={'inline'} /> all plans.
            </>
          )}
        </h1>
        <div className={'hide-scrollbar lg:overflow-x-auto lg:px-4 md:w-full'}>
          <div
            className={
              'mt-21 rounded-2xl bg-light/[0.37] p-12 lg:mt-8 lg:w-[1200px] lg:p-4'
            }
          >
            <div
              className={
                'grid grid-cols-[auto_repeat(3,240px)] items-end gap-x-10 lg:gap-x-4'
              }
            >
              <div />
              {PACKAGES.map((item) => {
                const { name, action, description } = item || {};
                return (
                  <div
                    key={item.id}
                    className={'flex flex-col items-center gap-y-4 md:gap-y-2'}
                  >
                    {
                      item.id === 1 &&
                        <p>
                          <b
                            className={`md:text-12 ${
                              item.id === 1 ? 'text-red' : 'text-transparent'
                            }`}
                          >
                            {item.id === 1 ? '480.000' : '.'}
                          </b> <b>VNĐ</b>
                        </p>
                    }
                    {/* @ts-ignore */}
                    <h4 className={'text-24 md:text-14'}>{trans[name]}</h4>
                    <Button
                      outline={item.id !== 1}
                      url={action.url}
                      className={'!rounded-[32px]'}
                    >
                      <>
                        {!!action.shortText && (
                          <span className={'text-14 md:text-12'}>
                            {/* @ts-ignore */}
                            {trans[action.shortText]}
                          </span>
                        )}
                        <Icon
                          name={'export-circle'}
                          className={item.id === 1 ? 'fill-white' : 'fill-blue'}
                        />
                      </>
                    </Button>
                    <p className={'text-center md:text-12'}>
                      {description[locale]}
                    </p>
                  </div>
                );
              })}
            </div>

            <div className={'space-y-8 py-12 md:space-y-2 md:py-2 md:text-12'}>
              <h4 className={'text-24 text-green md:text-12'}>
                {trans.essentials}
              </h4>
              <div className={'space-y-6 md:space-y-2'}>
                {COMPARE_PLANS.essentials.map((item, index) => (
                  <div
                    className={
                      'grid grid-cols-[auto_repeat(3,240px)] gap-x-10 md:gap-x-4'
                    }
                    key={index}
                  >
                    <div className={'flex items-center gap-x-4'}>
                      <Icon
                        name={item.icon}
                        className={'h-6 w-6 fill-blue md:h-[12px] md:w-[12px]'}
                      />
                      <b>{trans[item.title]}</b>
                    </div>
                    {index === 0 &&
                      item.values.map((value, idx) => (
                        <b className={'text-center'} key={idx}>
                          {value}
                        </b>
                      ))}
                    {index === 1 &&
                      item.values.map((value, idx) =>
                        <p className={'text-center'} key={idx}>
                          <b className={'text-red'}>+{value}</b> /{' '}
                          {trans.extraStudents}
                        </p>
                      )}
                  </div>
                ))}
              </div>
            </div>

            {COMPARE_PLANS.otherData.map((pack) => (
              <div
                className={
                  'space-y-8 border-t-1 border-natreul py-12 md:space-y-2 md:py-2 md:text-12'
                }
                key={pack.title}
              >
                <h4 className={'text-24 text-green md:text-12'}>
                  {trans[pack.title]}
                </h4>
                <div className={'space-y-6 md:space-y-2'}>
                  {pack.data.map((item, index) => (
                    <div
                      className={
                        'grid grid-cols-[auto_repeat(3,240px)] gap-x-10 md:gap-x-4'
                      }
                      key={index}
                    >
                      <div className={'flex items-center gap-x-4'}>
                        <Icon
                          name={item.icon}
                          className={
                            'h-6 w-6 fill-blue md:h-[12px] md:w-[12px]'
                          }
                        />
                        <b>{trans[item.title]}</b>
                      </div>
                      {item.values.map((val, idx) => (
                        <div key={idx} className={'text-center'}>
                          <Icon
                            name={val ? 'tick-circle' : 'close-circle'}
                            className={val ? 'fill-blue' : 'fill-red'}
                          />
                        </div>
                      ))}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Customer Support Section */}
      <CustomerSupport />

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export default Pricing;
