'use client';

import classNames from 'classnames';
import dayjs from 'dayjs';
import { useRouter } from 'next/router';
import React, { useCallback, useState } from 'react';
import { toast } from 'react-toastify';

import AppImage from '@/components/AppImage';
import TextGradient from '@/components/TextGradient';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import CenterInformationForm from '@/pages/get-quote/Components/CenterInformationForm';
import DemoScheduleForm from '@/pages/get-quote/Components/DemoScheduleForm';
import PersonalInformationForm from '@/pages/get-quote/Components/PersonalInformationForm';
import { createRequestDemo } from '@/services/api/request-demo';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const STEPS = ['personalInformation', 'centerInformation', 'demoSchedule'];

const PAGE_DATA = {
  title: {
    en: () => (
      <>
        <TextGradient text={'Get a quote.'} />
        It all start here.
      </>
    ),
    vi: () => (
      <>
        <TextGradient text={'Nhận báo giá.'} />
        Cùng đồng hành.
      </>
    ),
  },
  formTitle: [
    {
      en: 'Get the App quotation just in minutes!',
      vi: 'Chọn thời gian phù hợp với bạn!',
    },
    {
      en: 'Just a little more!',
      vi: 'Thêm một ít thông tin nữa nhé!',
    },
    {
      en: 'Choose the time that work for you!',
      vi: 'Chọn thời gian phù hợp với bạn!',
    },
  ],
};

const GetQuote = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;
  const [currentStep, setCurrentStep] = useState(1);
  const [payload, setPayload] = useState<any>({});

  const handleRequestDemo = useCallback(async (data: any) => {
    try {
      const res = await createRequestDemo(data);
      if (res.data) {
        toast.success(trans.sendSuccessfully);
        setCurrentStep(1);
      }
    } catch (error) {
      console.log('error', error);
    }
  }, []);

  const handleSave = useCallback(
    (data: any) => {
      setPayload(data);
      if (currentStep === 1) {
        setCurrentStep(2);
      } else if (currentStep === 2) {
        if (data.scheduleDemo) {
          setCurrentStep(3);
        } else {
          handleRequestDemo(data);
        }
      } else {
        const meetingTime = dayjs(data.meetingTime);
        handleRequestDemo({
          ...data,
          meetingDate: dayjs(data.meetingDate)
            .set('hour', meetingTime.hour())
            .set('minute', meetingTime.minute())
            .set('second', meetingTime.second())
            .toDate(),
        });
      }
    },
    [currentStep],
  );

  return (
    <Main meta={<Meta title={trans.getAQuote} description={''} />}>
      <section className={`${sectionClassName} mb-32 pt-0 md:mb-12`}>
        <div className={`grid gap-x-12 min-lg:grid-cols-[auto_664px]`}>
          <div className={`space-y-14`}>
            <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
              {PAGE_DATA.title[locale]()}
            </h1>
            <AppImage
              src={`/assets/images/get-quote-${currentStep}.png`}
              className={
                '!object-contain lg:mx-auto lg:max-w-[480px] md:hidden'
              }
            />
          </div>
          <div className={'rounded-2xl bg-light/[0.37] p-12 md:mt-12 md:p-6'}>
            <h4 className={'text-24 text-blue md:text-18 min-lg:text-center'}>
              {PAGE_DATA.formTitle?.[currentStep - 1]?.[locale]}
            </h4>
            <div
              className={
                'mb-12 mt-14 flex justify-between border-b-1 border-natreul pb-8 md:my-6 md:pb-6'
              }
            >
              {STEPS.map((step, index) => {
                const isSelected = index + 1 === currentStep;
                return (
                  <div
                    key={index}
                    className={
                      'relative flex flex-col items-center gap-y-6 md:gap-y-4'
                    }
                  >
                    <div
                      className={classNames(
                        'w-14 h-14 rounded-full bg-green flex items-center justify-center md:w-10 md:h-10',
                        {
                          'bg-green text-white': isSelected,
                          'bg-green/10 text-green': !isSelected,
                        },
                      )}
                    >
                      <b className={'text-24 md:text-16'}>{index + 1}</b>
                    </div>
                    <b
                      className={`text-center text-18 md:text-14 ${
                        !isSelected ? 'text-transparent' : ''
                      }`}
                    >
                      {/* @ts-ignore */}
                      {trans[step]}
                    </b>
                  </div>
                );
              })}
            </div>
            {currentStep === 1 && (
              <PersonalInformationForm onSave={handleSave} payload={payload} />
            )}
            {currentStep === 2 && (
              <CenterInformationForm
                payload={payload}
                onSave={handleSave}
                onBack={() => setCurrentStep(1)}
              />
            )}
            {currentStep === 3 && (
              <DemoScheduleForm
                payload={payload}
                onSave={handleSave}
                onBack={() => setCurrentStep(2)}
              />
            )}
          </div>
        </div>
      </section>
    </Main>
  );
};

export default GetQuote;
