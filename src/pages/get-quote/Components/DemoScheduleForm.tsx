import { yupResolver } from '@hookform/resolvers/yup';
import dayjs from 'dayjs';
import { useCallback, useMemo } from 'react';
import * as React from 'react';
import ReactDatePicker from 'react-datepicker';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';

import Button from '@/components/Button';
import Datepicker from '@/components/Datepicker';
import Icon from '@/components/Icon';
import TextError from '@/components/TextError';
import Toggle from '@/components/Toggle';
import useTrans from '@/hooks/useTrans';
import { formatDate } from '@/utils';

interface FormValues {
  meetingDate?: string;
  meetingTime?: string;
  meetingType?: string;
}

const DemoScheduleForm = ({ onSave, onBack, payload }: any) => {
  const trans = useTrans();

  const FormSchema = yup.object().shape({
    meetingDate: yup
      .date()
      .required(trans.fieldIsRequired.meetingDate)
      .nullable(),
    meetingTime: yup
      .date()
      .required(trans.fieldIsRequired.meetingTime)
      .nullable(),
    meetingType: yup.string().required(trans.fieldIsRequired.demoMeeting),
  });

  const {
    handleSubmit,
    control,
    watch,
    formState: { errors },
  } = useForm<FormValues>({
    // @ts-ignore
    resolver: yupResolver(FormSchema),
    defaultValues: {},
  });

  const meetingDateWatched = watch('meetingDate');
  const meetingTimeWatched = watch('meetingTime');

  const meetingTypeOptions = useMemo(() => {
    return [
      {
        label: trans.demoMeetingType,
        value: 'meeting-online',
      },
      {
        label: trans.meetingOnline,
        value: 'in-person',
      },
    ];
  }, []);

  const handleSave = useCallback((data: FormValues) => {
    onSave({ ...payload, ...data });
  }, []);

  return (
    <div className={'space-y-8'}>
      <Controller
        control={control}
        name={'meetingDate'}
        render={({ field: { onChange, value } }) => (
          <Datepicker
            label={trans.selectYourDate}
            value={value}
            minDate={new Date()}
            onChange={onChange}
            error={errors?.meetingDate?.message}
          />
        )}
      />

      <Controller
        control={control}
        name={'meetingTime'}
        render={({ field: { onChange, value } }) => (
          <div className={'space-y-2'}>
            <h4 className={'mb-2 text-blue'}>
              {trans.time} (GMT+7) <span className={'text-red'}>*</span>
            </h4>
            {/* @ts-ignore */}
            <ReactDatePicker
              className={'bg-natreul/30'}
              placeholderText={'HH:MM AA'}
              selected={value ? new Date(value) : null}
              onChange={onChange}
              showTimeSelect
              showTimeSelectOnly
              timeIntervals={15}
              timeCaption="Time"
              dateFormat="h:mm aa"
            />
            {errors?.meetingTime?.message && (
              <TextError text={errors?.meetingTime?.message} />
            )}
          </div>
        )}
      />
      <Controller
        control={control}
        name={'meetingType'}
        render={({ field: { onChange, value } }) => {
          return (
            <Toggle
              label={trans.demoMeeting}
              data={meetingTypeOptions}
              value={value}
              onChange={onChange}
              error={errors?.meetingType?.message}
            />
          );
        }}
      />
      <div className={'!mt-4 grid grid-cols-2 gap-x-6'}>
        <div />
        <p className={'text-12 italic text-dark/[0.56]'}>
          {trans.meetingTypeMessage}
        </p>
      </div>

      <div className={'border-t-1 border-natreul pt-12 md:pt-6'}>
        <h4 className={'text-24 text-blue md:text-18'}>
          {trans.MCLScheduleDemoMeeting}
        </h4>
        <ul className={'mt-6 space-y-5'}>
          <li className={'flex items-center gap-x-4'}>
            <Icon name={'clock-light'} className={'fill-dark'} />
            <p>45 {trans.mins}</p>
          </li>
          {meetingTimeWatched && meetingDateWatched && (
            <li className={'flex items-center gap-x-4'}>
              <Icon name={'calendar-light'} className={'fill-dark'} />
              <p>
                {formatDate(meetingTimeWatched, 'hh:mm')} -{' '}
                {formatDate(
                  dayjs(meetingTimeWatched).add(45, 'minutes'),
                  'hh:mm',
                )}
                , {formatDate(meetingDateWatched, 'dddd, MMM DD, YYYY')}
              </p>
            </li>
          )}
        </ul>
        <div className={'mt-10 flex items-center justify-between'}>
          <button onClick={onBack} className={'size-10 rounded-full bg-dark'}>
            <span className={'barlow text-18 text-white'}>←</span>
          </button>
          <Button
            className={'bg-green'}
            onClick={handleSubmit((data) => handleSave(data))}
          >
            {trans.completeYourRequest}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DemoScheduleForm;
