import { yupResolver } from '@hookform/resolvers/yup';
import { useCallback, useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';

import Button from '@/components/Button';
import Checkbox from '@/components/Checkbox';
import Input from '@/components/Input';
import type { ISelectOption } from '@/components/Select';
import Select from '@/components/Select';
import Toggle from '@/components/Toggle';
import useTrans from '@/hooks/useTrans';
import { convertDataToSelectModel, getProvinces } from '@/utils';

interface FormValues {
  hotline?: string;
  city?: string;
  district?: string;
  ward?: string;
  address?: string;
  studentQuantity?: number;
  classroomsQuantity?: number;
  plan?: string;
  scheduleDemo?: boolean;
}

const CenterInformationForm = ({ onSave, onBack, payload }: any) => {
  const trans = useTrans();
  const addressData = getProvinces();
  const [provinceList] = useState(addressData);
  const [districtList, setDistrictList] = useState<any>([]);
  const [wardList, setWardList] = useState([]);

  const FormSchema = yup.object().shape({
    hotline: yup
      .string()
      .trim()
      .matches(/^[0-9]*$/, trans.incorrectFormat)
      .required(trans.fieldIsRequired.schoolHotline),
    city: yup.string().required(trans.fieldIsRequired.city),
    district: yup.string().required(trans.fieldIsRequired.district),
    ward: yup.string().required(trans.fieldIsRequired.ward),
    address: yup.string().required(trans.fieldIsRequired.address),
    studentQuantity: yup
      .string()
      .trim()
      .matches(/^[0-9]*$/, trans.incorrectFormat)
      .required(trans.fieldIsRequired.studentQuantity),
    classroomsQuantity: yup
      .string()
      .trim()
      .matches(/^[0-9]*$/, trans.incorrectFormat)
      .required(trans.fieldIsRequired.classroomsQuantity),
    plan: yup.string().required(trans.fieldIsRequired.plan),
    scheduleDemo: yup.boolean(),
  });

  const {
    handleSubmit,
    control,
    setValue,
    watch,
    formState: { errors },
  } = useForm<FormValues>({
    // @ts-ignore
    resolver: yupResolver(FormSchema),
    defaultValues: {},
  });

  const cityWatched = watch('city');
  const districtWatched = watch('district');

  const planOptions = useMemo(() => {
    return [
      {
        value: 'starter',
        label: trans.starter,
      },
      {
        value: 'seed-plan',
        label: trans.seed,
      },
      {
        value: 'bud-plan',
        label: trans.bud,
      },
      {
        value: 'leaf-plan',
        label: trans.leaf,
      },
    ];
  }, []);

  const handleSave = useCallback((data: FormValues) => {
    onSave({ ...payload, ...data });
  }, []);

  return (
    <div className={'space-y-8'}>
      <Controller
        control={control}
        name={'hotline'}
        render={({ field: { onChange, value } }) => (
          <Input
            label={trans.schoolHotline}
            value={value}
            onChange={onChange}
            error={errors?.hotline && errors?.hotline?.message}
          />
        )}
      />
      <div className={'grid gap-6 min-lg:grid-cols-3'}>
        <Controller
          control={control}
          name="city"
          render={({ field: { value } }) => (
            <Select
              label={trans.city}
              value={
                convertDataToSelectModel(provinceList)?.find(
                  (city: ISelectOption) => city.label === value,
                ) || null
              }
              options={convertDataToSelectModel(provinceList)}
              placeholder={trans.selectField.city}
              onChange={(e) => {
                setValue('city', e.label, {
                  shouldDirty: true,
                  shouldValidate: true,
                });
                const chosenProvince = getProvinces()?.find(
                  (item: any) => item.id === e.value,
                );

                setDistrictList(
                  chosenProvince?.level2s?.map((district: any) => {
                    return {
                      value: district.id,
                      label: district.name,
                      ...district,
                    };
                  }),
                );

                setValue('district', '', { shouldDirty: true });
                setValue('ward', '', { shouldDirty: true });
              }}
              error={errors?.city?.message}
            />
          )}
        />

        <Controller
          control={control}
          name="district"
          render={({ field: { value } }) => {
            return (
              <Select
                label={trans.district}
                isDisabled={!cityWatched}
                value={
                  districtList?.find(
                    (district: ISelectOption) => district.label === value,
                  ) || null
                }
                options={districtList}
                placeholder={trans.selectField.district}
                onChange={(e) => {
                  setValue('district', e.label, {
                    shouldDirty: true,
                    shouldValidate: true,
                  });
                  const chosenDistrict = districtList.filter(
                    (districtItem: any) => districtItem.id === e.value,
                  );
                  setWardList(
                    chosenDistrict[0]?.level3s?.map((ward: any) => {
                      return { value: ward.id, label: ward.name };
                    }),
                  );
                  setValue('ward', '');
                }}
                error={errors?.district?.message}
              />
            );
          }}
        />

        <Controller
          control={control}
          name="ward"
          render={({ field: { value } }) => (
            <Select
              label={trans.ward}
              isDisabled={!districtWatched}
              value={
                wardList?.find((ward: ISelectOption) => ward.label === value) ||
                null
              }
              options={wardList}
              placeholder={trans.selectField.ward}
              onChange={(e) => {
                setValue('ward', e.label, {
                  shouldDirty: true,
                  shouldValidate: true,
                });
              }}
              error={errors?.ward?.message}
            />
          )}
        />
      </div>
      <Controller
        control={control}
        name={'address'}
        render={({ field: { onChange, value } }) => (
          <Input
            label={trans.address}
            value={value}
            onChange={onChange}
            error={errors?.address && errors?.address?.message}
          />
        )}
      />
      <Controller
        control={control}
        name={'studentQuantity'}
        render={({ field: { onChange, value } }) => (
          <Input
            label={trans.studentQuantity}
            value={value}
            onChange={onChange}
            error={errors?.studentQuantity && errors?.studentQuantity?.message}
          />
        )}
      />
      <Controller
        control={control}
        name={'classroomsQuantity'}
        render={({ field: { onChange, value } }) => (
          <Input
            label={trans.classroomsQuantity}
            value={value}
            onChange={onChange}
            error={
              errors?.classroomsQuantity && errors?.classroomsQuantity?.message
            }
          />
        )}
      />
      <Controller
        control={control}
        name={'plan'}
        render={({ field: { onChange, value } }) => {
          return (
            <Toggle
              label={trans.plan}
              data={planOptions}
              value={value}
              onChange={onChange}
              error={errors?.plan?.message}
            />
          );
        }}
      />

      <div className={'!my-12 flex items-center justify-between'}>
        <button onClick={onBack} className={'h-10 w-10 rounded-full bg-dark'}>
          <span className={'barlow text-18 text-white'}>←</span>
        </button>
        <Button
          className={'bg-green'}
          onClick={handleSubmit((data) => handleSave(data))}
        >
          {trans.sendRequest}
        </Button>
      </div>

      <Controller
        control={control}
        name={'scheduleDemo'}
        render={({ field: { onChange, value } }) => (
          <Checkbox
            title={trans.scheduleDemo}
            label={trans.scheduleDemoCheckboxLabel}
            onChange={onChange}
            value={value}
          />
        )}
      />
    </div>
  );
};

export default CenterInformationForm;
