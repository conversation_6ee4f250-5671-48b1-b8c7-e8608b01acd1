import { yupResolver } from '@hookform/resolvers/yup';
import { useCallback } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';

import Button from '@/components/Button';
import Input from '@/components/Input';
import useTrans from '@/hooks/useTrans';

interface FormValues {
  firstName?: string;
  lastName?: string;
  email?: string;
  title?: string;
}

const PersonalInformationForm = ({ onSave, payload }: any) => {
  const trans = useTrans();

  const FormSchema = yup.object().shape({
    firstName: yup.string().required(trans.fieldIsRequired.firstName),
    lastName: yup.string().required(trans.fieldIsRequired.lastName),
    email: yup
      .string()
      .email(trans.incorrectFormat)
      .required(trans.fieldIsRequired.email),
    title: yup.string().required(trans.fieldIsRequired.title),
  });

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<FormValues>({
    // @ts-ignore
    resolver: yupResolver(FormSchema),
    defaultValues: {},
  });

  const handleSave = useCallback((data: FormValues) => {
    onSave({ ...payload, ...data });
  }, []);

  return (
    <div className={'space-y-8 md:space-y-6'}>
      <div className={'grid gap-6 min-lg:grid-cols-2'}>
        <Controller
          control={control}
          name={'firstName'}
          render={({ field: { onChange, value } }) => (
            <Input
              label={trans.firstName}
              value={value}
              onChange={onChange}
              error={errors?.firstName && errors?.firstName?.message}
            />
          )}
        />
        <Controller
          control={control}
          name={'lastName'}
          render={({ field: { onChange, value } }) => (
            <Input
              label={trans.lastName}
              value={value}
              onChange={onChange}
              error={errors?.lastName && errors?.lastName?.message}
            />
          )}
        />
      </div>
      <Controller
        control={control}
        name={'email'}
        render={({ field: { onChange, value } }) => (
          <Input
            label={trans.email}
            value={value}
            onChange={onChange}
            error={errors?.email && errors?.email?.message}
          />
        )}
      />
      <Controller
        control={control}
        name={'title'}
        render={({ field: { onChange, value } }) => (
          <Input
            label={trans.title}
            value={value}
            onChange={onChange}
            error={errors?.title && errors?.title?.message}
          />
        )}
      />

      <div className={'flex min-lg:justify-end'}>
        <Button
          className={'bg-green'}
          onClick={handleSubmit((data) => handleSave(data))}
        >
          {trans.next}
        </Button>
      </div>
    </div>
  );
};

export default PersonalInformationForm;
