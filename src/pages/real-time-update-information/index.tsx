'use client';

import { useRouter } from 'next/router';
import React from 'react';

import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Button from '@/components/Button';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import TextGradient from '@/components/TextGradient';
import Title from '@/components/Title';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const HEADER = {
  subTitle: {
    en: 'Real-time update information',
    vi: 'Cập nhật thông tin nhanh chóng với thời gian thực',
  },
  title: {
    en: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Digitally'} className={'inline'} />{' '}
        {`share daily updates with families.`}
      </h1>
    ),
    vi: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Số hoá'} className={'inline'} />{' '}
        {`việc cập nhật thông tin hằng ngày.`}
      </h1>
    ),
  },
  description: {
    en: 'Send alerts and real-time updates to families. Share activities, lessons, notes, and memorable photos from that day. Report on children’s mood, meals, health status, nap schedules, and more. Further, will inform parents and guardians when supply is low.',
    vi: 'Gửi thông báo và cập nhật theo thời gian thực cho gia đình. Chia sẻ các hoạt động, bài học, ghi chú và những bức ảnh đáng nhớ trong ngày. Báo cáo về tâm trạng, bữa ăn, tình trạng sức khỏe, lịch ngủ trưa của trẻ, và nhiều thông tin khác. Hơn nữa, sẽ thông báo cho phụ huynh và người giám hộ khi các vật dụng cá nhân của trẻ sắp hết.',
  },
};

const HIGHLIGHTS = [
  {
    title: {
      en: 'Explained key activities and lesson learned',
      vi: 'Chia sẽ các hoạt động chính và chương trình giảng dạy',
    },
    description: {
      en: 'Highlight daily lesson plans, activities, and leaning progress of your kids. With one-click-maintain family involvement in each child’s developmental journey.',
      vi: 'Nêu bật các chương trình giảng dạy, hoạt động hàng ngày và tiến độ học tập của trẻ. Với một chạm, duy trì sự tham gia của gia đình vào hành trình phát triển của mỗi đứa trẻ.',
    },
    images: ['schedule.jpg', 'class_curriculum.jpg'],
  },
  {
    title: {
      en: 'Send information and alerts in real-time',
      vi: 'Gửi thông tin và báo cáo theo thời gian thực',
    },
    description: {
      en: 'Notify parents about important updates and information in real-time. From behavioral updates and unexpected illness to emergency center closure - instantly alert parents.',
      vi: 'Thông báo cho phụ huynh về các cập nhật và thông tin quan trọng theo thời gian thực. Từ cập nhật hành vi và bệnh tật bất ngờ đến việc đóng cửa trung tâm khẩn cấp - cũng sẽ được cảnh báo ngay lập tức cho phụ huynh.',
    },
    images: ['homepage.jpg', 'health_check.jpg'],
  },
  {
    title: {
      en: 'Inform families about essential supply',
      vi: 'Thông báo cho phụ huynh về nguồn cung cấp thiết yếu',
    },
    description: {
      en: 'Identify low areas of your supply stock. Ensure parents always have the essentials, like diapers, bottles, wipes, and more.',
      vi: 'Xác định và báo các các vật dụng sắp hết trong kho cung ứng của phụ huynh. Đảm bảo trẻ luôn có những thứ cần thiết như tã, bình sữa, khăn lau, v.v.',
    },
    images: ['sample.png', 'sample.png'],
  },
];

const QUOTE = {
  en: 'Boost parents engagement with digitalized daily reports',
  vi: 'Tăng cường sự tương tác của phụ huynh bằng các báo cáo kỹ thuật số hàng ngày',
};

const BENEFITS = {
  title: {
    en: 'Benefits of using Real-time Reporting',
    vi: 'Lợi ích của việc sử dụng Báo cáo thời gian thực',
  },
  data: [
    {
      icon: 'lovely',
      text: {
        en: 'Minimize manual work',
        vi: 'Giảm thiểu công việc thủ công',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Build trust',
        vi: 'Xây dựng sự tin tưởng từ phụ huynh',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Increase buy-in',
        vi: 'Thúc đẩy việc tuyển sinh vào trường',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Optimize the parent experience',
        vi: 'Tối ưu hóa trải nghiệm của phụ huynh',
      },
    },
  ],
};

const RealtimeUpdateInformation = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <Main
      meta={<Meta title={trans.realTimeUpdateInformation} description={''} />}
    >
      <section className={`${sectionClassName} pt-0`}>
        <div className={'grid grid-cols-[64%_auto] lg:grid-cols-1 lg:gap-y-2'}>
          <div className={'space-y-10 md:space-y-6'}>
            <h5 className={`barlow mb-10 text-18 text-red underline`}>
              <b>{HEADER.subTitle[locale]}</b>
            </h5>
            {HEADER.title[locale]()}
            <p className={'mb-14 mt-10 max-w-[506px]'}>
              {HEADER.description[locale]}
            </p>
            <div
              className={'flex items-center gap-8 lg:flex-col lg:items-start'}
            >
              <Button className={'!rounded-[30px] bg-white'}>
                <>
                  <span className={'text-dark'}>{trans.inviteYourSchool}</span>
                  <Icon name={'export-circle'} className={'fill-dark'} />
                </>
              </Button>
              <AppLink
                href={'/parents-application'}
                text={trans.seeItInAction}
              />
            </div>
          </div>
          <AppImage
            src="/assets/images/real-time-update-information/header.png"
            className={'!object-contain lg:mx-auto lg:max-w-[480px] md:hidden'}
          />
        </div>
      </section>

      <section className={sectionClassName}>
        <div className={'space-y-[112px] md:space-y-12'}>
          {HIGHLIGHTS.map((box, index) => (
            <div key={index} className={'space-y-12'}>
              <div
                className={'max-w-[70%] space-y-6 lg:max-w-full md:space-y-4'}
              >
                <Title text={box.title[locale]} />
                <p className={'barlow'}>{box.description[locale]}</p>
              </div>
              <div className={'grid grid-cols-2 gap-x-6 md:gap-x-4'}>
                {box.images.map((img, idx) => (
                  <div key={idx} className={'aspect-[640/400]'}>
                    <AppImage
                      src={`/assets/images/real-time-update-information/${img}`}
                      className={'rounded-lg'}
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      <section className={`${sectionClassName} max-w-full bg-blue`}>
        <h2
          className={
            'barlow--bold mx-auto max-w-[1048px] px-4 text-center text-48 text-yellow  lg:text-32 md:py-3 md:text-24'
          }
        >
          {QUOTE[locale]}
        </h2>
      </section>

      <section className={`${sectionClassName} max-w-[1600px]`}>
        <h5
          className={`barlow mb-16 text-center text-18 text-red underline md:mb-6 md:text-left`}
        >
          <b>{BENEFITS.title[locale]}</b>
        </h5>
        <div
          className={
            'grid grid-cols-4 gap-6 lg:grid-cols-2 md:grid-cols-1 md:gap-y-4'
          }
        >
          {BENEFITS.data.map((item, index) => (
            <div
              key={index}
              className={
                'flex flex-col items-center gap-y-6 rounded-2xl bg-white p-12 text-center shadow-normal md:flex-row md:gap-x-4 md:p-6 md:text-left'
              }
            >
              <div
                className={
                  'flex h-12 w-12 items-center justify-center rounded-full bg-blue/10'
                }
              >
                <Icon name={item.icon} className={'fill-blue'} />
              </div>
              <p>
                <b>{item.text[locale]}</b>
              </p>
            </div>
          ))}
        </div>
      </section>

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export default RealtimeUpdateInformation;
