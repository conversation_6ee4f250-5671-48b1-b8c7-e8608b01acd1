'use client';

import Link from 'next/link';
import { useRouter } from 'next/router';
import React from 'react';

import AppImage from '@/components/AppImage';
import Icon from '@/components/Icon';
import TextGradient from '@/components/TextGradient';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const HEADER = {
  title: {
    en: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        {`We're on a mission to`}
        <TextGradient
          text={'power the childcare industry.'}
          className={'ml-2 inline'}
        />
      </h1>
    ),
    vi: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        {`Chúng tôi thực hiện sứ mệnh`}
        <TextGradient
          text={'thúc đẩy chất lượng gi<PERSON>o dục mần non.'}
          className={'ml-2 inline'}
        />
      </h1>
    ),
  },
  description: {
    en: "At Mầm Chồi Lá, we believe early childhood learning centers play a pivotal role in a child's journey. That's why we've developed the first and only unified platform for the childcare industry with a full-featured customer relationship management (CRM), dedicated family app, and center management system (CMS) in one solution.",
    vi: 'Tại Mầm Chồi Lá, chúng tôi tin rằng các trung tâm giáo dục mầm non đóng vai trò then chốt trong hành trình phát triển của trẻ. Đó là lý do tại sao chúng tôi đã phát triển nền tảng toàn diện đầu tiên và duy nhất cho ngành chăm sóc trẻ em với giải pháp quản lý quan hệ khách hàng (CRM), ứng dụng dành riêng cho gia đình và hệ thống quản lý trung tâm (CMS) đầy đủ tính năng.',
  },
};

const MEMBERS = {
  title: {
    en: 'Meet the leadership team that is driving our mission forward',
    vi: 'Gặp gỡ đội ngũ lãnh đạo đang phát triển sứ mệnh của Mầm Chồi Lá',
  },
  data: [
    {
      avatar: '/assets/images/quocnguyen.png',
      fullName: 'Quốc Nguyễn',
      role: 'Co. Founder / Director',
      quote: {
        en: 'Old-fashioned geeks. Warmful uncle and brother. Space nerd. Dogs lover.',
        vi: 'Ông anh IT theo xu hướng cổ điển. Người chú, người anh ấm áp. Mọt sách không gian. Yêu chó chính hiệu.',
      },
      social: [
        {
          icon: 'sms',
          url: '<EMAIL>',
        },
        {
          icon: 'linkedin',
          url: '/',
        },
      ],
    },
    {
      avatar: '/assets/images/linhduong.png',
      fullName: 'Linh Dương',
      role: 'Co. Founder / Project Leader',
      quote: {
        en: 'Meticulous men. Lovely husban and father. Nature lover. Pet farmer.',
        vi: 'Người đàn ông kỹ tính. Người chồng, người bố đáng yêu. Yêu thiên nhiên. Thích chăm thú cưng.',
      },
      social: [
        {
          icon: 'sms',
          url: '<EMAIL>',
        },
        {
          icon: 'linkedin',
          url: '/',
        },
      ],
    },
    {
      avatar: '/assets/images/dunghoang.png',
      fullName: 'Dũng Hoàng',
      role: 'Co. Founder / Tech Lead',
      quote: {
        en: 'Patience guy. Good vibes brother.  Tech geeks. Sport player.',
        vi: 'Người đàn ông điềm tĩnh. Người anh có thiện cảm. Nghiện công nghệ. Đam mê thể thao.',
      },
      social: [
        {
          icon: 'sms',
          url: '<EMAIL>',
        },
        {
          icon: 'linkedin',
          url: '/',
        },
      ],
    },
    {
      avatar: '/assets/images/quypham.png',
      fullName: 'Quý Phạm',
      role: 'Co. Founder / Tech Lead',
      quote: {
        en: 'Mordern young guy. Cute newly husband.  Liberalism. Discovery passioner.',
        vi: 'Chàng trai trẻ hiện đại. Mới cưới vợ. Chủ nghĩa tự do. Yêu thích khám phá.',
      },
      social: [
        {
          icon: 'sms',
          url: '<EMAIL>',
        },
        {
          icon: 'linkedin',
          url: '/',
        },
      ],
    },
  ],
};

const Leadership = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <Main meta={<Meta title={trans.leadership} description={''} />}>
      <section className={`${sectionClassName} pt-0`}>
        <div className={'mx-auto min-lg:text-center'}>
          {HEADER.title[locale]()}
          <p className={'mx-auto mt-10 max-w-[930px] md:mt-6'}>
            {HEADER.description[locale]}
          </p>
        </div>
      </section>

      {/* Member Section */}
      <section className={`${sectionClassName} max-w-[1600px]`}>
        <h2
          className={
            'mx-auto mb-16 max-w-[900px] text-48 text-blue lg:text-32 md:mb-6 md:text-24 min-lg:text-center'
          }
        >
          {MEMBERS.title[locale]}
        </h2>
        <div className={`grid grid-cols-4 gap-10 lg:grid-cols-2 lg:gap-4`}>
          {MEMBERS.data.map((member, index) => (
            <div
              key={index}
              className={
                'group cursor-pointer rounded-2xl bg-white shadow-normal hover:bg-primaryGradient'
              }
            >
              <div
                className={
                  'overflow-hidden rounded-t-2xl lg:aspect-[170/160] min-lg:h-[400px]'
                }
              >
                <AppImage src={member.avatar} />
              </div>
              <div className={'p-12 group-hover:text-white md:p-6'}>
                <p className={'text-blue group-hover:text-white md:text-12'}>
                  <b>{member.role}</b>
                </p>
                <h2 className={'mb-8 mt-2 text-56 md:mb-4 md:text-18'}>
                  {member.fullName}
                </h2>
                <p className={'md:text-12'}>{member.quote[locale]}</p>
                <ul className={'mt-8 flex items-center gap-x-4'}>
                  {member.social.map((item, idx) => (
                    <li key={idx}>
                      <Link href={item.url} target={'_blank'}>
                        <Icon
                          name={item.icon}
                          className={'fill-blue group-hover:fill-white'}
                        />
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </section>
    </Main>
  );
};

export default Leadership;
