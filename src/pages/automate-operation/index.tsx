'use client';

import { useRouter } from 'next/router';
import React from 'react';

import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import TextGradient from '@/components/TextGradient';
import Title from '@/components/Title';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const HEADER = {
  subTitle: {
    en: 'Automated Operations',
    vi: 'Tự động hoá quy trình vận hành',
  },
  title: {
    en: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Simplify'} className={'inline'} />{' '}
        {`lead management.`}
      </h1>
    ),
    vi: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Đơn giản hoá'} className={'inline'} />{' '}
        {`việc quản lý vận hành.`}
      </h1>
    ),
  },
  description: {
    en: 'Missed opportunities happen when you manage leads with spreadsheets and sticky notes. You can’t find the information you need. Make it easy to manage, monitor, and follow up with leads – across locations.',
    vi: 'Cơ hội phát triển bị bỏ lỡ khi bạn quản lý thông tin công việc bằng các bảng tính và giấy tờ thủ công. Bạn không thể tìm thấy thông tin cần thiết nhanh chóng. Hãy làm cho việc quản lý, vận hành và theo dõi các cơ hội tiềm năng trở nên dễ dàng - mọi lúc mọi nơi.',
  },
};

const HIGHLIGHTS = [
  {
    id: 1,
    title: {
      en: `Work smarter, not harder: Organize leads in one place`,
      vi: 'Làm việc thông minh hơn: Sắp xếp thông tin trên cùng một nơi',
    },
    description: {
      en: 'Capture leads from anywhere – phone calls, email, text messages, website, and Facebook with Enroll. Family information is organized, so you can automatically send responses in real-time.',
      vi: 'Thu thập thông tin phụ huynh tiềm năng từ mọi nơi – cuộc gọi điện thoại, email, tin nhắn văn bản, trang web và Facebook cho việc Đăng ký tuyển sinh. Thông tin gia đình được sắp xếp theo trạng thái để bạn có thể tự động gửi phản hồi theo thời gian thực.',
    },
    listing: [
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Lead Management.</span> Manage
            family information, leads, follow-up tasks, and communications from
            a single screen.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>Quản lý thông tin.</span> Quản lý
            thông tin gia đình, phụ huynh tiềm năng, các công việc theo dõi và
            thông tin liên lạc từ một màn hình duy nhất.
          </>
        ),
      },
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>
              Childcare Listing Directory Integration.
            </span>{' '}
            Attract interest from leading childcare marketplaces. Generate and
            follow up with inquiries from childcare listing directories like
            Winnie and Kinside.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>
              Childcare Listing Directory Integration.
            </span>{' '}
            Attract interest from leading childcare marketplaces. Generate and
            follow up with inquiries from childcare listing directories like
            Winnie and Kinside.
          </>
        ),
      },
    ],
    images: ['sample.png', 'sample.png'],
  },
  {
    id: 2,
    title: {
      en: 'Master your to-do list',
      vi: 'Làm chủ danh sách công việc cần làm của bạn',
    },
    description: {
      en: "Managing tasks on sticky notes and Excel sheets won't cut it anymore. Master your to-do list with automated task management to save valuable time.",
      vi: 'Việc quản lý các công việc trên giấy ghi chú và bảng tính Excel sẽ không còn hiệu quả nữa với thời đạu công nghệ hiện tại. Làm chủ danh sách việc cần làm của bạn bằng tính năng quản lý tác vụ tự động để tiết kiệm hơn thời gian quý báu của bạn.',
    },
    listing: [
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Task Automation.</span> Quickly
            view your daily to-do list and complete essential enrollment tasks
            with just a few clicks. Use a Location Dashboard to get the quick
            insight you need. Plus, monitor staff productivity.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>Tự động hóa tác vụ.</span> Xem
            nhanh danh sách việc cần làm hàng ngày của bạn và hoàn thành các
            nhiệm vụ thiết yếu chỉ bằng vài cú nhấp chuột. Sử dụng Trang báo cáo
            tổng quan để có được thông tin chi tiết nhanh chóng mà bạn cần.
            Ngoài ra, có thể theo dõi năng suất của từng nhân viên một cách dễ
            dàng và nhanh chóng.
          </>
        ),
      },
    ],
    images: ['sample.png', 'sample.png'],
  },
  {
    id: 3,
    title: {
      en: 'Expertly oversee leads and your center on the go',
      vi: 'Giám sát cơ hội tiềm năng và tình trạng vận hành trung tâm của bạn một cách chuyên nghiệp mọi lúc mọi nơi',
    },
    description: {
      en: 'Utilize a mobile daycare CRM system to stay on top of new leads, tours, and tasks. Whether you’re at your desk or in the classroom - efficiently run your childcare business.',
      vi: 'Với hệ thống quản lý trung tâm giáo dục mầm non Mầm Chồi Lá, bạn có thể theo dõi quá trình vận hành và báo cáo tăng trường của trung tâm mọi lúc, mọi nơi, kể cả khi bạn đang làm việc trong phòng hay đang đi khảo sát ngay tại lớp học - giúp việc điều hành trung tâm của bạn hiệu quả hơn.',
    },
    listing: [
      {
        en: () => (
          <>
            <span className={'barlow--bold'}>Enroll Mobile App.</span> Follow up
            with families using email or text from the mobile app, without
            having to give out personal contact information. View tours or
            meetings to stay on top of tasks.
          </>
        ),
        vi: () => (
          <>
            <span className={'barlow--bold'}>
              Nền tảng đăng ký tuyển sinh trực tuyến.
            </span>{' '}
            Liên lạc với các gia đình bằng email hoặc tin nhắn từ các nền tảng
            trực tuyến mà không cần phải yêu cầu cung cấp thông tin liên hệ cá
            nhân. Đánh dấu các lịch tham quan và lịch hẹn phụ huynh luôn luôn
            được ưu tiên theo dõi.
          </>
        ),
      },
    ],
    images: ['sample.png', 'sample.png'],
  },
  {
    id: 4,
    title: {
      en: 'Manage your team with ease',
      vi: 'Quản lý đội ngũ nhân viên và vận hành một cách dễ dàng',
    },
    description: {
      en: 'Organize staff documents, maintain records, and simplify communication. Conveniently track staff hours with digital timekeeping.',
      vi: 'Sắp xếp tài liệu nhân viên, duy trì hồ sơ và đơn giản hóa việc liên lạc. Theo dõi giờ làm việc của nhân viên một cách thuận tiện bằng các thiết bị công nghệ thông minh.',
    },
    listing: [],
    images: ['sample.png', 'sample.png'],
  },
  {
    id: 5,
    title: {
      en: 'Simplify payroll processing',
      vi: 'Đơn giản hóa việc xử lý bảng lương hàng tháng',
    },
    description: {
      en: 'Easily review clock-in and clock-out times. Access and report on wages or staff timecards in one simple-to-use command center.',
      vi: 'Dễ dàng xem lại thời gian bấm giờ vào và bấm giờ ra. Truy cập và báo cáo về tiền lương hoặc bảng chấm công của nhân viên trong một trang quản lý dễ sử dụng.',
    },
    listing: [],
    images: ['sample.png', 'sample.png'],
  },
  {
    id: 6,
    title: {
      en: 'Effortlessly track staff hours',
      vi: 'Dễ dàng theo dõi giờ làm việc của nhân viên',
    },
    description: {
      en: 'Digitize staff management. Simplify the way you track hours and review timecards – with less work than ever before.',
      vi: 'Số hóa quản lý nhân viên. Đơn giản hóa cách bạn theo dõi giờ và xem lại bảng chấm công – đơn giản hơn bao giờ hết.',
    },
    listing: [],
    images: ['sample.png', 'sample.png'],
  },
];

const QUOTE = {
  en: 'Manage childcare center anytime, anywhere',
  vi: 'Quản lý trung tâm giáo dục của bạn hiệu quả hơn mọi lúc, mọi nơi',
};

const BENEFITS = {
  title: {
    en: 'Benefits of using Digitalized Attendance Tracking',
    vi: 'Lợi ích của việc Tự động quá quy trình vận hành',
  },
  data: [
    {
      icon: 'lovely',
      text: {
        en: 'Eliminate paper files',
        vi: 'Loại bỏ công việc giấy tờ thủ công',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Make staff scheduling easy',
        vi: 'Theo dõi hiệu suất làm việc của nhân viên dễ dàng',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Streamline operations',
        vi: 'Quan sát toàn bộ tổng quát tình hình vận hành của trung tâm',
      },
    },
    {
      icon: 'lovely',
      text: {
        en: 'Simplify communication',
        vi: 'Đơn giản hoá và chuẩn xác việc trao đổi thông tin giữa các bên',
      },
    },
  ],
};

const AutomateOperation = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <Main meta={<Meta title={trans.automateOperations} description={''} />}>
      <section className={`${sectionClassName} pt-0`}>
        <div className={'grid grid-cols-2 lg:grid-cols-1 lg:gap-y-2'}>
          <div className={'space-y-10 md:space-y-6'}>
            <h5 className={`barlow mb-10 text-18 text-red underline`}>
              <b>{HEADER.subTitle[locale]}</b>
            </h5>
            {HEADER.title[locale]()}
            <p className={'mb-14 mt-10 max-w-[506px]'}>
              {HEADER.description[locale]}
            </p>
            <div
              className={'flex items-center gap-8 lg:flex-col lg:items-start'}
            >
              <AppLink href={'/get-quote'} text={trans.requestADemo} />
              <AppLink
                href={'/principals-management'}
                text={trans.seeItInAction}
              />
            </div>
          </div>
          <AppImage
            src="/assets/images/automate-operation/header.png"
            className={'!object-contain lg:mx-auto lg:max-w-[480px] md:hidden'}
          />
        </div>
      </section>

      <section className={sectionClassName}>
        <div className={'space-y-[112px] md:space-y-12'}>
          {HIGHLIGHTS.map((box, index) => (
            <div key={index}>
              <div
                className={'max-w-[70%] space-y-6 lg:max-w-full md:space-y-4'}
              >
                <Title text={box.title[locale]} />
                <p className={'barlow'}>{box.description[locale]}</p>
              </div>
              <div
                className={'mb-12 mt-6 grid grid-cols-4 gap-6 lg:grid-cols-2'}
              >
                {box.listing.map((item, i) => (
                  <p key={i} className={'barlow'}>
                    {item[locale]()}
                  </p>
                ))}
              </div>
              <div className={'grid grid-cols-2 gap-x-6 md:gap-x-4'}>
                {box.images.map((img, idx) => (
                  <div key={idx} className={'aspect-[640/400]'}>
                    <AppImage
                      src={`/assets/images/automate-operation/${img}`}
                      className={'rounded-lg'}
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </section>

      <section className={`${sectionClassName} max-w-full bg-blue`}>
        <h2
          className={
            'barlow--bold mx-auto max-w-[1048px] px-4 text-center text-48 text-yellow  lg:text-32 md:py-3 md:text-24'
          }
        >
          {QUOTE[locale]}
        </h2>
      </section>

      <section className={`${sectionClassName} max-w-[1600px]`}>
        <h5
          className={`barlow mb-16 text-center text-18 text-red underline md:mb-6 md:text-left`}
        >
          <b>{BENEFITS.title[locale]}</b>
        </h5>
        <div
          className={
            'grid grid-cols-4 gap-6 lg:grid-cols-2 md:grid-cols-1 md:gap-y-4 md:px-0'
          }
        >
          {BENEFITS.data.map((item, index) => (
            <div
              key={index}
              className={
                'flex flex-col items-center gap-y-6 rounded-2xl bg-white p-12 text-center shadow-normal md:flex-row md:gap-x-4 md:p-6 md:text-left'
              }
            >
              <div
                className={
                  'flex h-12 w-12 items-center justify-center rounded-full bg-blue/10'
                }
              >
                <Icon name={item.icon} className={'fill-blue'} />
              </div>
              <p>
                <b>{item.text[locale]}</b>
              </p>
            </div>
          ))}
        </div>
      </section>

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export default AutomateOperation;
