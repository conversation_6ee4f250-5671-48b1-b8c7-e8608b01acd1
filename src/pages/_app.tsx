import '../styles/global.scss';
import 'swiper/css';

import type { AppProps } from 'next/app';
import { <PERSON> } from 'next/font/google';
import { ThemeProvider } from 'next-themes';
import { ToastContainer } from 'react-toastify';

import Context from '@/context/context';

const barlow = Barlow({
  weight: ['400', '500', '700'],
  style: ['normal', 'italic'],
  subsets: ['latin'],
  variable: '--font-barlow',
});

const MyApp = ({ Component, pageProps }: AppProps) => (
  <Context>
    <main className={barlow.variable}>
      <ThemeProvider attribute="class">
        <Component {...pageProps} />
        <ToastContainer
          position="top-right"
          autoClose={8000}
          hideProgressBar={false}
          newestOnTop={false}
          draggable={false}
          pauseOnFocusLoss
          closeOnClick
          pauseOnHover
        />
      </ThemeProvider>
    </main>
  </Context>
);

export default MyApp;
