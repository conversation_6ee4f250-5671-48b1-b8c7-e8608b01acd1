'use client';

import Image from 'next/image';
import React from 'react';
import ReactPlayer from 'react-player/lazy';

import FaqListing from '@/components/Faq';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import SuggestForm from '@/components/SuggestForm';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';

const Driver = () => {
  const [openTab, setOpenTab] = React.useState(1);
  return (
    <Main meta={<Meta title={''} description={''} />}>
      <div
        className={`mx-auto flex h-full min-h-[820px] flex-col justify-between pt-[120px]`}
      >
        {/* Header Page Section */}
        <div className={`mb-[88px] px-24 pt-[120px]`}>
          <div
            className={`mx-auto mb-[40px] flex h-full w-full max-w-[1728px] flex-col items-baseline justify-center px-24 text-left`}
          >
            <p
              className={`text-sm mb-[24px] font-bold text-[#EF4060] underline`}
            >
              Bus Driver tracking app
            </p>
            <p className={`mb-[24px] text-[72px] font-normal text-[#58595B]`}>
              <span
                className={`bg-gradient-to-tr from-[#EE9CA7] to-[#FFDDE1] bg-clip-text font-bold text-transparent`}
              >
                Hello, Bus Captain!
              </span>
              <br />
              this app is for you.
            </p>
            <p className={`w-[35vw] text-[#58595B]`}>
              Designed to streamline attendance tracking and route management
              for the convenience of both parents and schools.
            </p>
            <Image
              src="/assets/images/driver-app-head-banner.png"
              alt="Help Banner Images"
              width={500}
              height={500}
              className={`absolute right-[10vw] h-[400px] w-[680px] rounded-[16px] object-cover`}
            />
          </div>
        </div>

        {/* App Hightlights Section */}
        <div
          className={`mx-auto mb-[88px] flex h-full w-full max-w-[1728px] flex-col items-baseline justify-center gap-y-10 px-48 text-left`}
        >
          <p className={`text-[48px] font-bold text-[#21409A]`}>
            Bus Drivers love Mầm Chồi Lá
          </p>
          <div
            className={`items-normal flex w-full flex-row justify-between gap-x-8`}
          >
            {/* App Hightlights Item Section */}
            <div className={`w-[25vw]`}>
              <p className={`text-[24px] font-bold text-[#58595B]`}>
                Simplified Attendance Tracking.
              </p>
              <p className={`my-6 text-[#58595B]`}>
                Our application eliminates the need for manual attendance
                taking, streamlining the process for bus drivers. With just a
                few taps on their mobile devices, they can easily record and
                update student attendance, saving time and reducing paperwork.
              </p>
              <Image
                src="/assets/images/about-image.jpg"
                alt="Help Banner Images"
                width={500}
                height={500}
                className={`h-[240px] w-full rounded-[16px] object-cover`}
              />
            </div>
            {/* App Hightlights Item Section */}
            <div className={`w-[25vw]`}>
              <p className={`text-[24px] font-bold text-[#58595B]`}>
                Real-Time Route Tracking.
              </p>
              <p className={`my-6 text-[#58595B]`}>
                The application offers real-time GPS tracking, providing bus
                drivers with accurate information about their current location
                and the designated routes. This feature enables them to navigate
                efficiently, ensuring on-time transportation and minimizing
                delays.
              </p>
              <Image
                src="/assets/images/about-image.jpg"
                alt="Help Banner Images"
                width={500}
                height={500}
                className={`h-[240px] w-full rounded-[16px] object-cover`}
              />
            </div>
            {/* App Hightlights Item Section */}
            <div className={`w-[25vw]`}>
              <p className={`text-[24px] font-bold text-[#58595B]`}>
                Improved Safety and Security.
              </p>
              <p className={`my-6 text-[#58595B]`}>
                {`With real-time GPS tracking, bus drivers and parents can have
                peace of mind knowing the exact location of the bus at all
                times. This enhances safety and security for students, allowing
                parents to track the bus's progress and ensuring that students
                are transported safely to and from school.`}
              </p>
              <Image
                src="/assets/images/about-image.jpg"
                alt="Help Banner Images"
                width={500}
                height={500}
                className={`h-[240px] w-full rounded-[16px] object-cover`}
              />
            </div>
          </div>
        </div>

        {/* Hightlight Video */}
        <div className={`mx-auto mb-[168px]`}>
          <ReactPlayer url="https://www.youtube.com/watch?v=WCZvsLAv4B0" />
        </div>

        {/* Key Functions Section */}
        <div
          className={`mx-auto mb-[88px] flex h-full w-full max-w-[1728px] flex-col items-center justify-center gap-y-10 px-48 text-center`}
        >
          <p className={`text-sm font-bold text-[#EF4060] underline`}>
            Feature that bus drivers could
          </p>
          <p className={`text-[48px] font-bold text-[#21409A]`}>
            Drive, report fast and convenience
          </p>
          <div className={`w-full`}>
            {/* Tab Content */}
            <div className={`relative flex w-full flex-col break-words`}>
              <div className={`flex-auto px-4 py-16`}>
                <div className={`tab-content tab-space`}>
                  {/* Content Item */}
                  <div
                    className={`${openTab === 1 ? 'block' : 'hidden'}`}
                    id="link1"
                  >
                    <p>Attendance Tracking</p>
                  </div>
                  {/* Content Item */}
                  <div
                    className={`${openTab === 2 ? 'block' : 'hidden'}`}
                    id="link2"
                  >
                    <p>Developmental Progress</p>
                  </div>
                  {/* Content Item */}
                  <div
                    className={`${openTab === 3 ? 'block' : 'hidden'}`}
                    id="link3"
                  >
                    <p>Activities Tracking</p>
                  </div>
                  {/* Content Item */}
                  <div
                    className={`${openTab === 4 ? 'block' : 'hidden'}`}
                    id="link4"
                  >
                    <p>Health Status Update</p>
                  </div>
                  {/* Content Item */}
                  <div
                    className={`${openTab === 5 ? 'block' : 'hidden'}`}
                    id="link5"
                  >
                    <p>Detailed Daily Reports</p>
                  </div>
                  {/* Content Item */}
                  <div
                    className={`${openTab === 6 ? 'block' : 'hidden'}`}
                    id="link6"
                  >
                    <p>Working Performance</p>
                  </div>
                  {/* Content Item */}
                  <div
                    className={`${openTab === 7 ? 'block' : 'hidden'}`}
                    id="link7"
                  >
                    <p>Online Payroll Receive</p>
                  </div>
                </div>
              </div>
            </div>
            {/* Tab Buttons */}
            <ul
              className={`mb-[24px] mt-[18px] flex list-none flex-row flex-wrap justify-between gap-x-8 pb-4 pt-3`}
              role="tablist"
            >
              {/* Tab Buttons Item */}
              <li className={`flex flex-col items-center gap-y-6 text-center`}>
                <a
                  className={`
                    group/tab
                    w-[fit-content]
                    rounded-full
                    p-4
                    transition-all
                    ease-in-out hover:bg-[#21409A]/100
                    ${openTab === 1 ? 'bg-[#21409A]/100' : 'bg-[#21409A]/10'}`}
                  onClick={(e) => {
                    e.preventDefault();
                    setOpenTab(1);
                  }}
                  data-toggle="tab"
                  href="#link1"
                  role="tablist"
                >
                  <Icon
                    color={'white'}
                    name={'airdrop'}
                    className={`
                      group-hover/tab:[&>*]:fill-[#FDFDFD]
                      ${
                        openTab === 1
                          ? '[&>*]:fill-[#FDFDFD]'
                          : '[&>*]:fill-[#21409A]'
                      }`}
                    width={48}
                    height={48}
                  />
                </a>
                <span className={`text-[16px] font-bold text-[#58595B]`}>
                  Attendance
                  <br />
                  Tracking
                </span>
              </li>
              {/* Tab Buttons Item */}
              <li className={`flex flex-col items-center gap-y-6 text-center`}>
                <a
                  className={`
                    group/tab
                    w-[fit-content]
                    rounded-full
                    p-4
                    transition-all
                    ease-in-out hover:bg-[#21409A]/100
                    ${openTab === 2 ? 'bg-[#21409A]/100' : 'bg-[#21409A]/10'}`}
                  onClick={(e) => {
                    e.preventDefault();
                    setOpenTab(2);
                  }}
                  data-toggle="tab"
                  href="#link1"
                  role="tablist"
                >
                  <Icon
                    color={'white'}
                    name={'airdrop'}
                    className={`
                      group-hover/tab:[&>*]:fill-[#FDFDFD]
                      ${
                        openTab === 2
                          ? '[&>*]:fill-[#FDFDFD]'
                          : '[&>*]:fill-[#21409A]'
                      }`}
                    width={48}
                    height={48}
                  />
                </a>
                <span className={`text-[16px] font-bold text-[#58595B]`}>
                  Developmental
                  <br />
                  Progress
                </span>
              </li>
              {/* Tab Buttons Item */}
              <li className={`flex flex-col items-center gap-y-6 text-center`}>
                <a
                  className={`
                    group/tab
                    w-[fit-content]
                    rounded-full
                    p-4
                    transition-all
                    ease-in-out hover:bg-[#21409A]/100
                    ${openTab === 3 ? 'bg-[#21409A]/100' : 'bg-[#21409A]/10'}`}
                  onClick={(e) => {
                    e.preventDefault();
                    setOpenTab(3);
                  }}
                  data-toggle="tab"
                  href="#link1"
                  role="tablist"
                >
                  <Icon
                    color={'white'}
                    name={'airdrop'}
                    className={`
                      group-hover/tab:[&>*]:fill-[#FDFDFD]
                      ${
                        openTab === 3
                          ? '[&>*]:fill-[#FDFDFD]'
                          : '[&>*]:fill-[#21409A]'
                      }`}
                    width={48}
                    height={48}
                  />
                </a>
                <span className={`text-[16px] font-bold text-[#58595B]`}>
                  Activities
                  <br />
                  Tracking
                </span>
              </li>
              {/* Tab Buttons Item */}
              <li className={`flex flex-col items-center gap-y-6 text-center`}>
                <a
                  className={`
                    group/tab
                    w-[fit-content]
                    rounded-full
                    p-4
                    transition-all
                    ease-in-out hover:bg-[#21409A]/100
                    ${openTab === 4 ? 'bg-[#21409A]/100' : 'bg-[#21409A]/10'}`}
                  onClick={(e) => {
                    e.preventDefault();
                    setOpenTab(4);
                  }}
                  data-toggle="tab"
                  href="#link1"
                  role="tablist"
                >
                  <Icon
                    color={'white'}
                    name={'airdrop'}
                    className={`
                      group-hover/tab:[&>*]:fill-[#FDFDFD]
                      ${
                        openTab === 4
                          ? '[&>*]:fill-[#FDFDFD]'
                          : '[&>*]:fill-[#21409A]'
                      }`}
                    width={48}
                    height={48}
                  />
                </a>
                <span className={`text-[16px] font-bold text-[#58595B]`}>
                  Health Status
                  <br />
                  Update
                </span>
              </li>
              {/* Tab Buttons Item */}
              <li className={`flex flex-col items-center gap-y-6 text-center`}>
                <a
                  className={`
                    group/tab
                    w-[fit-content]
                    rounded-full
                    p-4
                    transition-all
                    ease-in-out hover:bg-[#21409A]/100
                    ${openTab === 5 ? 'bg-[#21409A]/100' : 'bg-[#21409A]/10'}`}
                  onClick={(e) => {
                    e.preventDefault();
                    setOpenTab(5);
                  }}
                  data-toggle="tab"
                  href="#link1"
                  role="tablist"
                >
                  <Icon
                    color={'white'}
                    name={'airdrop'}
                    className={`
                      group-hover/tab:[&>*]:fill-[#FDFDFD]
                      ${
                        openTab === 5
                          ? '[&>*]:fill-[#FDFDFD]'
                          : '[&>*]:fill-[#21409A]'
                      }`}
                    width={48}
                    height={48}
                  />
                </a>
                <span className={`text-[16px] font-bold text-[#58595B]`}>
                  Detailed Daily
                  <br />
                  Reports
                </span>
              </li>
              {/* Tab Buttons Item */}
              <li className={`flex flex-col items-center gap-y-6 text-center`}>
                <a
                  className={`
                    group/tab
                    w-[fit-content]
                    rounded-full
                    p-4
                    transition-all
                    ease-in-out hover:bg-[#21409A]/100
                    ${openTab === 6 ? 'bg-[#21409A]/100' : 'bg-[#21409A]/10'}`}
                  onClick={(e) => {
                    e.preventDefault();
                    setOpenTab(6);
                  }}
                  data-toggle="tab"
                  href="#link1"
                  role="tablist"
                >
                  <Icon
                    color={'white'}
                    name={'airdrop'}
                    className={`
                      group-hover/tab:[&>*]:fill-[#FDFDFD]
                      ${
                        openTab === 6
                          ? '[&>*]:fill-[#FDFDFD]'
                          : '[&>*]:fill-[#21409A]'
                      }`}
                    width={48}
                    height={48}
                  />
                </a>
                <span className={`text-[16px] font-bold text-[#58595B]`}>
                  Working
                  <br />
                  Performance
                </span>
              </li>
              {/* Tab Buttons Item */}
              <li className={`flex flex-col items-center gap-y-6 text-center`}>
                <a
                  className={`
                    group/tab
                    w-[fit-content]
                    rounded-full
                    p-4
                    transition-all
                    ease-in-out hover:bg-[#21409A]/100
                    ${openTab === 7 ? 'bg-[#21409A]/100' : 'bg-[#21409A]/10'}`}
                  onClick={(e) => {
                    e.preventDefault();
                    setOpenTab(7);
                  }}
                  data-toggle="tab"
                  href="#link1"
                  role="tablist"
                >
                  <Icon
                    color={'white'}
                    name={'airdrop'}
                    className={`
                      group-hover/tab:[&>*]:fill-[#FDFDFD]
                      ${
                        openTab === 7
                          ? '[&>*]:fill-[#FDFDFD]'
                          : '[&>*]:fill-[#21409A]'
                      }`}
                    width={48}
                    height={48}
                  />
                </a>
                <span className={`text-[16px] font-bold text-[#58595B]`}>
                  Online Payroll
                  <br />
                  Receive
                </span>
              </li>
            </ul>
          </div>
        </div>

        {/* Special Quote Section */}
        <div className={`mb-[112px] min-h-[100px] bg-[#21409A]`}>
          <div
            className={`items-normal mx-auto flex h-full w-full max-w-[1728px] flex-col justify-center gap-y-[40px] px-24 py-20 text-center`}
          >
            <p className={`text-[28px] font-bold text-[#FFDE2F]`}>
              Enabling you to efficiently manage attendance records
              <br />
              and track routes in a user-friendly and intuitive manner
            </p>
          </div>
        </div>

        {/* FAQ */}
        <FaqListing />

        {/* Suggest Form Section */}
        <SuggestForm />

        {/* Quote Section */}
        <Quote />
      </div>
    </Main>
  );
};

export default Driver;
