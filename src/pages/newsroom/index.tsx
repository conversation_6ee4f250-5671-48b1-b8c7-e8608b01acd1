'use client';

// eslint-disable-next-line import/no-extraneous-dependencies
import { debounce } from 'lodash';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import TextGradient from '@/components/TextGradient';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { getNewsCategories } from '@/services/api/newsCategories';
import { getNews } from '@/services/api/newsroom';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';
import type { INews } from '@/typings/news';
import { convertToSlug, formatDate } from '@/utils';
import { getErrorMessage } from '@/utils/error';

const HEADER = {
  title: {
    en: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient text={'Build'} className={'inline'} />{' '}
        {`meaningful relationships with your enrolled families.`}
      </h1>
    ),
    vi: () => (
      <h1 className={'text-72 font-normal lg:text-56 md:text-40'}>
        <TextGradient
          text={'Ý tưởng & cảm hứng dành cho bạn'}
          className={'inline'}
        />{' '}
        để vận hành trung tâm giáo dục hiệu quả.
      </h1>
    ),
  },
};

const Newsroom = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  // const [loading, setLoading] = useState(false);
  // const [pageCount, setPageCount] = useState(0);
  const [pageIndex, setPageIndex] = useState(0);
  const [searchKey, setSearchKey] = useState('');
  const [topBlogs, setTopBlogs] = useState<INews[]>([]);
  const [blogs, setBlogs] = useState<INews[]>([]);
  const [blogCategories, setBlogCategories] = useState<any>([]);
  const [blogCategorySelected, setBlogCategorySelected] = useState<any>(null);

  useEffect(() => {
    (async () => {
      try {
        const res = await getNewsCategories();

        if (res.data) {
          setBlogCategories(res.data.data);
        }
      } catch (error) {
        toast.error(getErrorMessage(error));
      }
    })();
  }, []);

  useEffect(() => {
    (async () => {
      try {
        setTopBlogs([]);
        const res = await getNews({
          pageSize: 4,
          page: 1,
        });
        if (res.data) {
          setTopBlogs(res.data?.data);
        }
      } catch (error) {
        toast.error(getErrorMessage(error));
      }
    })();
  }, []);

  useEffect(() => {
    (async () => {
      try {
        setBlogs([]);
        const filters = {
          $and: [
            {
              $or: [
                {
                  newsId: { $containsi: searchKey },
                },
                {
                  title: {
                    $or: [
                      {
                        en: { $containsi: searchKey },
                      },
                      {
                        vi: { $containsi: searchKey },
                      },
                    ],
                  },
                },
              ],
            },
            {
              id: { $notIn: topBlogs?.map((blog) => blog.id) },
            },
            {
              category: {
                id: blogCategorySelected
                  ? { $eq: blogCategorySelected }
                  : { $ne: blogCategorySelected },
              },
            },
          ],
        };
        const res = await getNews(
          {
            pageSize: 9,
            page: pageIndex + 1,
          },
          filters,
        );
        if (res.data) {
          setBlogs(res.data?.data);
          // setPageCount(res?.data?.meta?.pageCount);
        }
      } catch (error) {
        toast.error(getErrorMessage(error));
      }
    })();
  }, [pageIndex, searchKey, topBlogs, blogCategorySelected]);

  const handleSearchKeyChange = useCallback(
    debounce((value) => {
      setSearchKey(value);
      setPageIndex(0);
    }, 500),
    [],
  );

  return (
    <Main meta={<Meta title={trans.newsroom} description={''} />}>
      <>
        <section className={`${sectionClassName} pt-0`}>
          <div className={'mb-10 space-y-10 px-8 text-center md:space-y-6'}>
            {HEADER.title[locale]()}
          </div>
        </section>

        <section className={sectionClassName}>
          <div className={'grid grid-cols-2 gap-x-8 lg:grid-cols-1 lg:gap-y-4'}>
            <Link
              href={`/newsroom/${convertToSlug(topBlogs[0]?.title?.[locale])}?id=${topBlogs[0]?.id}`}
            >
              {!!topBlogs[0]?.thumbnail?.url && (
                <Image
                  src={topBlogs[0].thumbnail.url}
                  alt="Blog Thumbnail"
                  width={500}
                  height={500}
                  className={'aspect-[1.67] w-full overflow-hidden rounded-lg'}
                />
              )}
              <div className={'my-8 space-x-2 lg:my-4'}>
                <div
                  className={
                    'inline-flex h-[34px] items-center justify-center rounded-md border-1 border-black px-8'
                  }
                >
                  <p className={'text-14'}>
                    {formatDate(topBlogs[0]?.createdAt, 'DD/MM/YYYY hh:mm')}
                  </p>
                </div>
                <div
                  className={
                    'inline-flex h-[34px] items-center justify-center rounded-2xl bg-blue px-8'
                  }
                >
                  <p className={'text-14 text-white'}>
                    {topBlogs[0]?.category?.name?.[locale]}
                  </p>
                </div>
              </div>
              <h2 className={'text-24 lg:text-18'}>
                <b>{topBlogs[0]?.title?.[locale]}</b>
              </h2>
              <div
                className="mt-8 line-clamp-2 text-dark/[.56] lg:mt-4"
                dangerouslySetInnerHTML={{
                  __html:
                    topBlogs[0]?.content?.[locale as keyof ILanguageInput] ||
                    ' ',
                }}
              />
            </Link>
            <div className={'space-y-8'}>
              {topBlogs?.slice(1, 3).map((blog: INews) => (
                <Link
                  href={`/newsroom/${convertToSlug(blog?.title?.[locale])}?id=${blog?.id}`}
                  className={'flex gap-x-4'}
                  key={blog?.id}
                >
                  {!!blog?.thumbnail?.url && (
                    <Image
                      src={blog?.thumbnail?.url}
                      alt="Blog Thumbnail"
                      width={500}
                      height={0}
                      className={
                        'aspect-[148/120] min-w-[230px] self-baseline overflow-hidden rounded-lg lg:min-w-[148px]'
                      }
                    />
                  )}
                  <div className={'space-y-4 lg:space-y-2'}>
                    <div className={'flex flex-wrap gap-2'}>
                      <div
                        className={
                          'inline-flex h-[34px] items-center justify-center rounded-md border-1 border-black px-8 lg:h-6 lg:px-4'
                        }
                      >
                        <p className={'text-14 lg:text-12'}>
                          {formatDate(blog?.createdAt, 'DD/MM/YYYY hh:mm')}
                        </p>
                      </div>
                      <div
                        className={
                          'inline-flex h-[34px] items-center justify-center rounded-2xl bg-blue px-8 lg:h-6 lg:px-4'
                        }
                      >
                        <p className={'text-14 text-white lg:text-12'}>
                          {blog?.category?.name?.[locale]}
                        </p>
                      </div>
                    </div>
                    <h2 className={'text-18 lg:line-clamp-2 lg:text-14'}>
                      <b>{blog?.title?.[locale]}</b>
                    </h2>
                    <div
                      className="line-clamp-3 max-h-[72px] text-14 text-dark/[.56] lg:text-12"
                      dangerouslySetInnerHTML={{
                        __html:
                          blog?.content?.[locale as keyof ILanguageInput] || '',
                      }}
                    />
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </section>

        <section className={`${sectionClassName} lg:px-0`}>
          <div className={'flex gap-x-16 lg:flex-col'}>
            <div className={'flex items-center gap-x-2 self-baseline lg:px-4'}>
              <Icon name="search-normal" />
              <input
                type="text"
                placeholder={trans.searchNews}
                onChange={(e) => handleSearchKeyChange(e.currentTarget.value)}
                className={`h-10 w-[200px] bg-transparent px-2 transition-all duration-500 placeholder:text-black/30 min-lg:text-24`}
              />
            </div>
            <div className={'flex gap-3 pt-2 lg:flex-col'}>
              <p className={'whitespace-nowrap lg:px-4 min-lg:text-24'}>
                Lọc theo danh mục:
              </p>
              <div
                className={
                  'hide-scrollbar flex flex-wrap gap-2 lg:flex-nowrap lg:overflow-x-auto lg:px-4'
                }
              >
                {blogCategories?.map((category: any) => {
                  const isSelected = category?.id === blogCategorySelected;
                  return (
                    <button
                      key={category?.id}
                      className={`inline-flex h-[34px] items-center justify-center rounded-2xl border-1 border-blue px-6 text-blue ${
                        isSelected ? 'bg-blue text-white' : ''
                      }`}
                      onClick={() =>
                        setBlogCategorySelected(
                          blogCategorySelected === category?.id
                            ? null
                            : category?.id,
                        )
                      }
                    >
                      <b className={'whitespace-nowrap'}>
                        {category?.name[locale]}
                      </b>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
          <div
            className={'mt-10 grid grid-cols-3 gap-6 lg:grid-cols-1 lg:px-4'}
          >
            {blogs?.map((blog: INews) => (
              <Link
                href={`/newsroom/${convertToSlug(blog?.title?.[locale])}?id=${blog?.id}`}
                className={'lg:flex lg:gap-x-4'}
                key={blog?.id}
              >
                {!!blog?.thumbnail?.url && (
                  <Image
                    src={blog.thumbnail.url}
                    alt="Blog Thumbnail"
                    width={500}
                    height={500}
                    className={
                      'aspect-[400/320] w-full self-baseline overflow-hidden rounded-lg lg:aspect-[148/120] lg:min-w-[148px]'
                    }
                  />
                )}
                <div>
                  <div className={'my-6 flex flex-wrap gap-2 lg:my-2 lg:mt-0'}>
                    <div
                      className={
                        'inline-flex h-[34px] items-center justify-center rounded-md border-1 border-black px-8 lg:h-6 lg:px-4'
                      }
                    >
                      <p className={'text-14 lg:text-12'}>
                        {formatDate(blog?.createdAt, 'DD/MM/YYYY hh:mm')}
                      </p>
                    </div>
                    <div
                      className={
                        'inline-flex h-[34px] items-center justify-center rounded-2xl bg-blue px-8 lg:h-6 lg:px-4'
                      }
                    >
                      <p className={'text-14 text-white lg:text-12'}>
                        {blog?.category?.name?.[locale]}
                      </p>
                    </div>
                  </div>
                  <h2 className={'text-18 lg:text-14'}>
                    <b>{blog?.title?.[locale]}</b>
                  </h2>
                  <div
                    className="mt-6 line-clamp-2 text-dark/[.56] lg:mt-2 lg:text-12"
                    dangerouslySetInnerHTML={{
                      __html:
                        blog?.content?.[locale as keyof ILanguageInput] || '',
                    }}
                  />
                </div>
              </Link>
            ))}
          </div>
        </section>

        <Quote />
      </>
    </Main>
  );
};

export default Newsroom;
