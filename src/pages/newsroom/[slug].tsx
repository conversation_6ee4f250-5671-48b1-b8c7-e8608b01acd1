// @ts-ignore
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useMemo } from 'react';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { FacebookShareButton } from 'react-share';
import { toast } from 'react-toastify';

import AppImage from '@/components/AppImage';
import Icon from '@/components/Icon';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { fetchNews, fetchNewsById } from '@/services/api/newsroom';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';
import type { INews } from '@/typings/news';
import { convertToSlug, formatDate } from '@/utils';

interface IProps {
  blogData: INews;
  relatedBlogs: INews[];
}

const NewsroomDetail = ({ blogData, relatedBlogs }: IProps) => {
  const trans = useTrans();
  const { asPath } = useRouter();
  const locale = useRouter().locale as keyof ILanguageInput;

  const { category, title, content, thumbnail, createdAt } = blogData || {};

  const blogTags = useMemo(() => {
    let tags = [category?.name?.[locale]];
    if (category?.parent) {
      tags = [category?.parent?.name?.[locale], ...tags];
    }
    return tags;
  }, [category]);

  return (
    <Main
      meta={
        <Meta
          description={''}
          title={title?.[locale] || ''}
          images={[{ url: thumbnail?.url }]}
        />
      }
    >
      <section className={'lg:py-18 py-[126px] md:py-16'}>
        <div className={'mx-auto max-w-[1336px] px-4'}>
          <div
            className={
              'flex flex-wrap items-center gap-x-8 text-24 text-primary lg:gap-x-4 md:text-18'
            }
          >
            <h4>{trans.newsroom}</h4>
            <span>/</span>
            <h4>
              {blogTags?.map((tag, index) => {
                return (
                  <>
                    <span key={index}>{tag}</span>
                    {index < blogTags.length - 1 && <span> . </span>}
                  </>
                );
              })}
            </h4>
            <span>/</span>
            <p>{title?.[locale]}</p>
          </div>

          <div
            className={'mb-6 mt-10 flex items-center gap-x-4 md:mb-4 md:mt-6'}
          >
            <Icon name={'calendar'} className={'fill-dark'} />
            <p className={'text-18 md:text-12'}>
              {formatDate(createdAt, 'DD/MM/YYYY')}
            </p>
          </div>

          <div>
            <h2
              className={'mb-6 text-40 uppercase text-black md:mb-4 md:text-18'}
            >
              {title?.[locale]}
            </h2>

            {thumbnail && (
              <div className={'mb-6'}>
                <AppImage src={thumbnail?.url} />
              </div>
            )}

            <div
              dangerouslySetInnerHTML={{
                __html: content?.[locale] || '',
              }}
            />

            {!!blogData && (
              <div className={'my-12 space-x-5 lg:mt-8 md:mt-6'}>
                <b>{trans.shareOn}:</b>
                {typeof window !== 'undefined' && (
                  <>
                    <FacebookShareButton
                      url={`${window?.location?.origin}${asPath}`}
                    >
                      <Icon name={'facebook'} className={'fill-primary'} />
                    </FacebookShareButton>
                    {/* @ts-ignore */}
                    <CopyToClipboard
                      text={`${window.location.origin}${asPath}`}
                      onCopy={() => toast.success(trans.copied)}
                    >
                      <button>
                        <Icon name={'link'} className={'fill-primary'} />
                      </button>
                    </CopyToClipboard>
                  </>
                )}
              </div>
            )}

            {!!relatedBlogs?.length && (
              <div className={'space-y-6 md:space-y-4'}>
                <h3 className={'text-32 text-primary md:text-16'}>
                  {trans.relatedNews}
                </h3>
                <div
                  className={
                    'grid grid-cols-4 gap-x-8 md:grid-cols-1 md:gap-y-6'
                  }
                >
                  {relatedBlogs.map((blog) => (
                    <Link
                      href={`/newsroom/${convertToSlug(blog?.title?.[locale])}?id=${blog?.id}`}
                      className={'lg:flex lg:gap-x-4'}
                      key={blog?.id}
                    >
                      {!!blog?.thumbnail?.url && (
                        <Image
                          src={blog.thumbnail.url}
                          alt="Blog Thumbnail"
                          width={500}
                          height={500}
                          className={
                            'aspect-[400/320] w-full self-baseline overflow-hidden rounded-lg lg:aspect-[148/120] lg:min-w-[148px]'
                          }
                        />
                      )}
                      <div>
                        <div
                          className={
                            'my-6 flex flex-wrap gap-2 lg:my-2 lg:mt-0'
                          }
                        >
                          <div
                            className={
                              'inline-flex h-[34px] items-center justify-center rounded-md border-1 border-black px-8 lg:h-6 lg:px-4'
                            }
                          >
                            <p className={'text-14 lg:text-12'}>
                              {formatDate(blog?.createdAt, 'DD/MM/YYYY hh:mm')}
                            </p>
                          </div>
                          <div
                            className={
                              'inline-flex h-[34px] items-center justify-center rounded-2xl bg-blue px-8 lg:h-6 lg:px-4'
                            }
                          >
                            <p className={'text-14 text-white lg:text-12'}>
                              {blog?.category?.name?.[locale]}
                            </p>
                          </div>
                        </div>
                        <h2 className={'text-18 lg:text-14'}>
                          <b>{blog?.title?.[locale]}</b>
                        </h2>
                        <div
                          className="mt-6 line-clamp-2 text-dark/[.56] lg:mt-2 lg:text-12"
                          dangerouslySetInnerHTML={{
                            __html:
                              blog?.content?.[locale as keyof ILanguageInput] ||
                              ' ',
                          }}
                        />
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </section>
    </Main>
  );
};

export async function getServerSideProps(context: any) {
  const { id } = context.query;
  const { host } = context.req.headers;

  try {
    const blogRes = await fetchNewsById(id);
    const blogData = await blogRes.json();

    const pagination = {
      pageSize: 4,
      page: 1,
    };
    const filters = {
      category: blogData?.data?.category?.id,
      id: {
        $ne: id,
      },
    };
    const relatedBlogsRes = await fetchNews(pagination, filters, {
      clientdomain: host,
    });
    const relatedBlogsData = await relatedBlogsRes.json();

    return {
      props: {
        blogData: blogData.data,
        relatedBlogs: relatedBlogsData.data || [],
      },
    };
  } catch (error) {
    // push('/newsroom');
  }
  return {
    props: {},
  };
}
export const runtime = 'experimental-edge';
export default NewsroomDetail;
