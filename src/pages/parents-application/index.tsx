'use client';

import { useRouter } from 'next/router';
import React from 'react';
import ReactPlayer from 'react-player/lazy';

import AppHighlightSection from '@/components/AppHighlighSection';
import AppImage from '@/components/AppImage';
import AppLink from '@/components/AppLink';
import Button from '@/components/Button';
import FaqListing from '@/components/Faq';
import FunctionsSection from '@/components/FunctionsSection';
import Icon from '@/components/Icon';
import Quote from '@/components/Quote';
import SuggestForm from '@/components/SuggestForm';
import TextGradient from '@/components/TextGradient';
import { sectionClassName } from '@/constants';
import useTrans from '@/hooks/useTrans';
import { Meta } from '@/layouts/Meta';
import { Main } from '@/templates/Main';
import type { ILanguageInput } from '@/typings/common';

const HEADER = {
  title: {
    en: 'Parent engagement app',
    vi: 'Ứng dụng tương tác dành cho <PERSON>ụ huynh',
  },
  description: {
    en: 'Communicate with your childcare center and receive updates about your child’s day in real time so you never miss those special moments again!',
    vi: 'Liên lạc với trung tâm giáo dục của con bạn và nhận thông tin cập nhật về ngày của trẻ với thời gian thực để bạn không bao giờ bỏ lỡ bất kỳ khoảnh khắc đặc biệt nào nữa!',
  },
  hey: {
    en: 'Hey, Parent!',
    vi: 'Xin chào, Phụ huynh!',
  },
  thisAppIsForYou: {
    en: 'this app is for you',
    vi: 'cùng tìm hiểu nhé.',
  },
};

const HIGHLIGHT = {
  title: {
    en: 'Parents/Families love Mầm Chồi Lá',
    vi: 'Phụ huynh và Gia đình yêu thích Mầm Chồi Lá',
  },
  data: [
    {
      title: {
        en: 'Connect with educators.',
        vi: 'Liên lạc với Giáo viên.',
      },
      description: {
        en: 'A strong parent-teacher relationship can have direct impact on your child’s development Mầm Chồi Lá enables you to have deeper conversations with teachers!',
        vi: 'Mối quan hệ phụ huynh - giáo viên bền chặt có thể tác động trực tiếp đến sự phát triển của con bạn, vì vậy Mầm Chồi Lá giúp bạn trò chuyện sâu sắc hơn với giáo viên của trẻ!',
      },
      image: '/assets/images/parent_love_mcl_1.png',
    },
    {
      title: {
        en: 'Never miss a WOW moment.',
        vi: 'Nắm bắt toàn bộ khoảnh khắc.',
      },
      description: {
        en: 'Getting visibility into your child’s development can be difficult especially when teachers barely have time to sit and chat. Get daily reports on your child’s progress with Mầm Chồi Lá!',
        vi: 'Việc nắm bắt được sự phát triển của con bạn có thể khó khăn, đặc biệt khi bạn và giáo viên không có thời gian để trao đổi với nhau mỗi ngày. Thay vào đó, nhận báo cáo hàng ngày về quá trình phát triển con bạn với Mầm Chồi Lá!',
      },
      image: '/assets/images/parent_love_mcl_2.png',
    },
    {
      title: {
        en: 'Never miss a payment again.',
        vi: 'Không bị trễ bất kỳ khoản thanh toán nào.',
      },
      description: {
        en: 'It can be a pain carrying cash to pay child care bills in thi digital world and with Mầm Chồi Lá you can pay anywhere, an time.',
        vi: 'Việc mang theo tiền mặt để thanh toán các hóa đơn trong thời đại kỹ thuật số này có thể là một điều khó khăn. Với Mầm Chồi Lá, bạn có thể thanh toán trực tuyến mọi lúc, mọi nơi.',
      },
      image: '/assets/images/parent_love_mcl_3.png',
    },
  ],
};

const FUNCTIONS = {
  subTitle: {
    en: 'Feature that parent could',
    vi: 'Mầm Chồi Lá cung cấp tính năng để phụ huynh có thể',
  },
  title: {
    en: 'See everything your child is up to!',
    vi: 'Quan sát toàn bộ những gì đang diễn ra xung quanh trẻ!',
  },
  data: [
    {
      id: 1,
      icon: 'gallery',
      subTitle: {
        en: 'Photo & Messages',
        vi: 'Tin nhắn & Hình ảnh',
      },
      title: {
        en: 'Update messages, photos, videos, and newsletters',
        vi: 'Cập nhật tin nhắn, hình ảnh, video và tin tức',
      },
      description: {
        en: "Keep parents closely connected to your child's educational journey. With this app, you can easily stay up-to-date with your child's latest learning progress, developmental milestones, and the achievements they earn at school.",
        vi: 'Giao tiếp trực tiếp với giáo viên thông qua ứng dụng dành cho phụ huynh và nhận được tin nhắn, video và ảnh của con bạn.',
      },
    },
    {
      id: 2,
      icon: 'trend-up',
      subTitle: {
        en: 'Developmental Progress',
        vi: 'Quá trình học tập',
      },
      title: {
        en: 'Stay Informed and Engaged with your child developmental milestones',
        vi: 'Luôn cập nhật thông tin và theo dõi các mốc phát triển của con bạn',
      },
      description: {
        en: "Takes school activity tracking to the next level. With our advanced AI camera check-in system, you can effortlessly monitor all school activities and keep an eye on your child's day.",
        vi: 'Giúp phụ huynh kết nối chặt chẽ với hành trình giáo dục của con bạn. Với Mầm Chồi Lá, bạn có thể dễ dàng cập nhật tiến độ học tập mới nhất của con bạn, các mốc phát triển và thành tích chúng đạt được ở trường.',
      },
    },
    {
      id: 3,
      icon: 'gps',
      subTitle: {
        en: 'Activities Tracking',
        vi: 'Theo dõi hoạt động',
      },
      title: {
        en: 'Effortless School Activity Tracking with AI Camera Check-in and Mobile Parent App',
        vi: 'Theo dõi hoạt động ở trường dễ dàng với tính năng điểm danh bằng camera AI và ứng dụng dành cho Phụ huynh',
      },
      description: {
        en: "Our Parent App goes the extra mile to ensure you have all the tools you need to monitor your child's health and well-being in real time.\n\nOur Parent App provides you with the tools to stay proactive about your child's health, nutrition, and overall well-being. With real-time updates, you can make informed decisions to keep your child happy and healthy during their time at kindergarten.",
        vi: 'Đưa việc theo dõi hoạt động ở trường lên một tầm cao mới. Với hệ thống điểm danh bằng camera AI tiên tiến của Mầm Chồi Lá, bạn có thể dễ dàng theo dõi mọi hoạt động ở trường và cập nhật một ngày đi học của con mình.',
      },
    },
    {
      id: 4,
      icon: 'heart',
      subTitle: {
        en: 'Health Status Update',
        vi: 'Cập nhật tình trạng sức khoẻ',
      },
      title: {
        en: 'Child Health Monitoring Made Easy',
        vi: 'Kiểm tra & theo dõi sức khỏe của trẻ được thực hiện dễ dàng',
      },
      description: {
        en: "Our Parent App goes the extra mile to ensure you have all the tools you need to monitor your child's health and well-being in real time.\n\nOur Parent App provides you with the tools to stay proactive about your child's health, nutrition, and overall well-being. With real-time updates, you can make informed decisions to keep your child happy and healthy during their time at kindergarten.",
        vi: 'Ứng dụng dành cho phụ huynh của Mầm Chồi Lá luôn luôn nỗ lực hơn nữa để đảm bảo bạn có tất cả các công cụ cần thiết để theo dõi sức khỏe và thể chất của con bạn trong thời gian thực.\n\nỨng dụng dành cho phụ huynh cung cấp cho bạn các công cụ để luôn chủ động về sức khỏe, dinh dưỡng và sức khỏe tổng thể của con bạn. Với thông tin cập nhật theo thời gian thực, bạn có thể đưa ra quyết định sáng suốt để giữ cho con bạn vui vẻ và khỏe mạnh trong thời gian học mẫu giáo.',
      },
    },
    {
      id: 5,
      icon: 'document-text',
      subTitle: {
        en: 'Detailed Daily Reports',
        vi: 'Báo cáo chi tiết hằng ngày',
      },
      title: {
        en: "Stay Organized and Informed with Your Child's School Schedule",
        vi: 'Luôn sắp xếp và thông báo về lịch sử học tập của con bạn',
      },
      description: {
        en: "Our Parent App keeps you in the loop by providing easy access to your child's school schedule and a comprehensive daily report of their activities at school.\n\nWith our Parent App, you have the tools to ensure your child's school schedule aligns with your daily routine. Stay informed about their day and be an active participant in their educational journey.",
        vi: 'Ứng dụng dành cho phụ huynh của Mầm Chồi Lá giúp bạn luôn cập nhật bằng cách cung cấp quyền truy cập dễ dàng vào lịch học ở trường của con bạn và báo cáo toàn diện hàng ngày về các hoạt động của trẻ ở trường.\n\nVới Ứng dụng dành cho phụ huynh, bạn có các công cụ để đảm bảo lịch học của con bạn phù hợp với thời gian hằng ngày của bản thân. Luôn cập nhật thông tin về ngày của trẻ và luôn tham gia tích cực vào hành trình giáo dục của trẻ.',
      },
    },
    {
      id: 6,
      icon: 'security-user',
      subTitle: {
        en: 'Authorized Pickup',
        vi: 'Uỷ quyền đón trẻ',
      },
      title: {
        en: 'Effortless and Secure Child Pickup Management',
        vi: 'Quản lý việc đón trẻ dễ dàng và an toàn',
      },
      description: {
        en: 'With our Parent App, you have complete control over who can pick up your child, making it easy to ensure their safety and well-being. Share QR codes with confidence, knowing your child is in safe hands.',
        vi: 'Với Ứng dụng dành cho phụ huynh của Mầm Chồi Lá, bạn có toàn quyền kiểm soát ai có thể đón con bạn, giúp dễ dàng đảm bảo an toàn và sức khỏe của trẻ. Chủ động chia sẻ mã QR vì biết rằng con bạn luôn luôn an toàn.',
      },
    },
    {
      id: 7,
      icon: 'profile-2user',
      subTitle: {
        en: 'Invite whole Families',
        vi: 'Đồng quản lý cùng gia đình',
      },
      title: {
        en: 'Effortless Co-Parenting and Shared Management',
        vi: 'Dễ dàng cùng nuôi dạy con cái và quản lý chung',
      },
      description: {
        en: "Our Parent App believes in the power of teamwork when it comes to managing your child's profile.\n\nWith our shared management features, you'll never miss a beat when it comes to managing your child's profile and ensuring their well-being. Co-parenting has never been more seamless and efficient.",
        vi: 'Ứng dụng dành cho phụ huynh của Mầm Chồi Lá tin vào sức mạnh của sự hợp tác khi quản lý hồ sơ của con bạn.\n\nVới các tính năng quản lý chung, bạn sẽ không bao giờ bỏ lỡ cơ hội quản lý hồ sơ của con mình và đảm bảo sức khỏe của trẻ. Việc cùng làm cha mẹ chưa bao giờ liền mạch và hiệu quả hơn trước.',
      },
    },
    {
      id: 8,
      icon: 'card-pos',
      subTitle: {
        en: 'Easy Online Payment',
        vi: 'Thanh toán phí trực tuyến',
      },
      title: {
        en: 'Simplified School Fee Management with Versatile Payment Options',
        vi: 'Quản lý học phí đơn giản với các tùy chọn thanh toán linh hoạt',
      },
      description: {
        en: "Our Parent App strives to make your life easier by simplifying the process of paying school fees, extra curriculum charges, and outdoor event fees.\n\nWith our Parent App, you have the power to manage your finances efficiently, ensuring your child's education, activities, and events are well taken care of. Say goodbye to the hassle of traditional payments and embrace the convenience of online transactions.",
        vi: 'Ứng dụng dành cho phụ huynh của Mầm Chồi Lá cố gắng giúp cuộc sống của bạn dễ dàng hơn bằng cách đơn giản hóa quy trình đóng học phí, chương trình ngoại khoá và sự kiện ngoài trời của trường.\n\nVới Ứng dụng dành cho phụ huynh, bạn có khả năng quản lý tài chính của mình một cách hiệu quả, đảm bảo việc giáo dục, hoạt động và sự kiện của con bạn được chăm sóc chu đáo. Hãy tạm biệt những rắc rối của thanh toán truyền thống và đón nhận sự tiện lợi của giao dịch trực tuyến.',
      },
    },
  ],
};

const CUSTOMER_REVIEWS = [
  {
    name: {
      en: 'Emma',
      vi: 'Thuỳ Anh',
    },
    title: {
      en: 'Mom of 3 years old boy',
      vi: 'Mẹ của bé trai 3 tuổi',
    },
    content: {
      en: `“Love being able to see how my child’s day is going with quick and easy updates.”`,
      vi: `“Tôi thích việc có thể xem ngày của con tôi diễn ra như thế nào với những cập nhật nhanh chóng và dễ dàng.”`,
    },
  },
  {
    name: {
      en: 'David',
      vi: 'Hùng Sơn',
    },
    title: {
      en: 'Dad of 2 years old twin girls',
      vi: 'Bố của cặp song sinh 2 tuổi',
    },
    content: {
      en: `“So easy to use! I love that I can interact with my child’s teacher.”`,
      vi: `“Rất dễ sử dụng! Tôi thích việc tôi có thể tương tác với giáo viên của con tôi.”`,
    },
  },
  {
    name: {
      en: 'Eliza',
      vi: 'Thuỳ Vân',
    },
    title: {
      en: 'Mom of 4 years old girl',
      vi: 'Mẹ của bé gái 4 tuổi',
    },
    content: {
      en: `“I use this as both a parent and a teacher. I love being able to keep track of my child’s day.”`,
      vi: `“Tôi sử dụng với tư cách là một phụ huynh và một giáo viên. Tôi thích việc có thể theo dõi hoạt động hằng ngày của con tôi.”`,
    },
  },
  {
    name: {
      en: 'Tammy',
      vi: 'Thanh Tâm',
    },
    title: {
      en: 'Mom of 2 years old girl',
      vi: 'Mẹ của bé gái 2 tuổi',
    },
    content: {
      en: `“Really love this! Its easy to remind me when I have to pay my kids school fee and easy pay with online methods. Also clear tracking for all transaction that I have made.”`,
      vi: `“Thực sự thích điều này! Thật dễ dàng để nhắc nhở tôi khi phải trả học phí cho con mình và dễ dàng thanh toán bằng các phương thức trực tuyến. Cũng theo dõi rõ ràng cho tất cả các giao dịch tôi đã thực hiện.”`,
    },
  },
];

const QUOTE = {
  en: 'Engagement app allows parents to get updates on their children in real-time throughout the day',
  vi: 'Ứng dụng tương tác cho phép phụ huynh nhận thông tin cập nhật về trẻ theo thời gian thực suốt cả ngày',
};

const ParentsApplication = () => {
  const trans = useTrans();
  const locale = useRouter().locale as keyof ILanguageInput;

  return (
    <Main meta={<Meta title={trans.parentsApplication} description={''} />}>
      {/* Header Page Section */}
      <section className={sectionClassName}>
        <div className={'grid grid-cols-[50%_auto] gap-x-4 lg:grid-cols-1'}>
          <div className={'space-y-10 md:space-y-6'}>
            <h5 className={`barlow text-18 text-red underline md:text-14`}>
              <b>{HEADER.title[locale]}</b>
            </h5>
            <div>
              <TextGradient
                text={HEADER.hey[locale]}
                className={'whitespace-nowrap'}
              />
              <p className={'whitespace-nowrap text-72 lg:text-48 md:text-40'}>
                {HEADER.thisAppIsForYou[locale]}
              </p>
            </div>
            <p>{HEADER.description[locale]}</p>
            <div
              className={'flex items-center gap-8 lg:flex-col lg:items-start'}
            >
              <Button className={'!rounded-[30px] bg-white'}>
                <>
                  <span className={'text-dark'}>{trans.inviteYourSchool}</span>
                  <Icon name={'export-circle'} className={'fill-dark'} />
                </>
              </Button>
              <AppLink href={'/'} text={trans.seeItInAction} />
            </div>
          </div>
          <div>
            <AppImage
              src="/assets/images/parent-app-head-banner.png"
              className={'!object-contain md:mt-8'}
            />
          </div>
        </div>
      </section>

      <AppHighlightSection
        title={HIGHLIGHT.title[locale]}
        data={HIGHLIGHT.data}
      />

      {/* Highlight Video */}
      <section className={sectionClassName}>
        <div className={'flex justify-center'}>
          <div
            className={
              'mx-auto h-[400px] w-full max-w-[770px] overflow-hidden rounded-2xl md:aspect-[358/220] md:h-auto'
            }
          >
            <ReactPlayer
              url="https://www.youtube.com/watch?v=WCZvsLAv4B0"
              width={'100%'}
              height={'100%'}
            />
          </div>
        </div>
      </section>

      {/* Key Functions Section */}
      <FunctionsSection
        url={'parents-application'}
        subTitle={FUNCTIONS.subTitle[locale]}
        title={FUNCTIONS.title[locale]}
        data={FUNCTIONS.data}
      />

      <section className={`${sectionClassName} max-w-[1600px] md:px-0`}>
        <div
          className={
            'hide-scrollbar grid grid-cols-4 gap-6 lg:grid-cols-2 md:flex md:gap-x-4 md:overflow-x-auto'
          }
        >
          {CUSTOMER_REVIEWS.map((review, index) => (
            <div
              key={index}
              className={
                'flex flex-col items-center justify-between rounded-2xl bg-white p-12 text-center shadow-normal md:min-w-[280px] md:self-baseline md:p-6 md:shadow-none'
              }
            >
              <p className={'barlow text-24'}>{review.content[locale]}</p>
              <div className={'mt-14 text-18'}>
                <p>
                  <b>{review.name[locale]}</b>,
                </p>
                <p className={'text-natreul'}>{review.title[locale]}</p>
                <div className={'mx-auto mt-6 w-[152px]'}>
                  <AppImage src={'/assets/images/star-rating.png'} />
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Special Quote Section */}
      <section className={`${sectionClassName} max-w-full bg-blue`}>
        <h2
          className={
            'barlow--bold mx-auto max-w-[1048px] px-4 text-center text-48 text-yellow  lg:text-32 md:py-3 md:text-24'
          }
        >
          {QUOTE[locale]}
        </h2>
      </section>

      {/* FAQ */}
      <FaqListing />

      {/* Suggest Form Section */}
      <SuggestForm />

      {/* Quote Section */}
      <Quote />
    </Main>
  );
};

export default ParentsApplication;
