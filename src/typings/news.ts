import type { ILanguageInput, IMedia } from './common';

export interface INewsCategory {
  id: string;
  news?: INews[];
  parent?: INewsCategory;
  subCategories?: INewsCategory[];
  name?: ILanguageInput;
  slug?: ILanguageInput;
  school?: any;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;
  categoryId?: string;
}

export interface INews {
  id: string;
  newsId?: string;
  title?: ILanguageInput;
  content?: ILanguageInput;
  slug?: ILanguageInput;
  views?: number;
  category?: INewsCategory;
  status?: string;
  thumbnail?: IMedia;
  school?: any;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string | null;
  createdBy?: any;
  updatedBy?: any;
}
