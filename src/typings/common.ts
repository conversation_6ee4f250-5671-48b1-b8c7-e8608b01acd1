export type MultipleLanguage = {
  id: string | number;
  en: string | undefined;
  vi: string | undefined;
};

export type Pagination<T> = {
  data: T;
  meta: {
    pagination: {
      page: number;
      pageCount: number;
      pageSize: number;
      total: number;
    };
  };
};

export interface ILanguageInput {
  vi?: string;
  en?: string;
}

export interface IMedia {
  name?: string;
  filename: string;
  id: string;
  uploadedAt: string | null;
  url: string;
}

export type AppError = any;
