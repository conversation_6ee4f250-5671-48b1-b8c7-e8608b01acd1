# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# storybook
storybook-static

# cypress
cypress/screenshots
cypress/videos

# next.js
/.next
/out

# next-sitemap
public/robots.txt
public/sitemap.xml
public/sitemap-*.xml

# cache
.swc/

# production
/build

# misc
.DS_Store
*.pem
Thumbs.db

# debug
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# local folder
local

# vercel
.vercel
.idea
.env