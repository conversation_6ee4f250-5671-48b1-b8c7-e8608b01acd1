import { error } from '@/public/lang/en/error';
import { homePage } from '@/public/lang/en/homePage';

export const en = {
  aboutUs: 'About Us',
  trackingManagement: 'Tracking & Management',
  engageCommunication: 'Engage & Communication',
  realTimeUpdateInformation: 'Real-time Update Information',
  classroomManagement: 'Classroom Management',
  digitalPlanningTeaching: 'Digital Planning & Teaching',
  transparencyWorkPerformancePayroll: 'Transparency Work Performance & Payroll',
  workPerformancePayroll: 'Work Performance & Payroll',
  onlineEnrollment: 'Online Enrollment',
  billingPayment: 'Billing & Payment',
  performanceReports: 'Performance Reports',
  automateOperations: 'Automate Operations',
  forParents: 'For Parents',
  forEducators: 'For Educators',
  forSchoolOperations: 'For School Operations',
  leading: 'Leading',
  technologies: 'Technologies',
  iOTAIDevicesIntegration: 'iOT AI Devices Integration',
  latestTechApplied: 'Latest Tech Applied',
  extreme: 'Extreme',
  conveniences: 'Conveniences',
  maximizedAutomation: 'Maximized Automation',
  mobilePortals: 'Mobile Portals',
  digitals: 'Digitals',
  transparentHistory: 'Transparent History',
  visualizedReports: 'Visualized Reports',
  requestADemo: 'Request a demo',
  exploreMore: 'Explore more',
  moreDetail: 'More detail',
  howWeWorks: 'How we works?',
  weBringMoreBenefitsForYou: 'We bring more benefits for you',
  hotline: 'Hotline',
  getAQuote: 'Get a quote',
  quote: 'Price',
  solution: 'Solution',
  platforms: 'Platforms',
  pricing: 'Pricing',
  resources: 'Resources',
  login: 'Login',
  demoLink: 'Demo website',
  address: 'Address',
  company: 'Company',
  southAreaSales: 'South Area Sales',
  customerSupportLine: 'Customer Support Line',
  contactSales: 'Contact Sales',
  engageApplicationForParents: 'Engage Application (For Parents)',
  educationManagementApplicationForEducators:
    'Education Management Application (For Teachers / Nannies)',
  drivingApplicationForSchoolBusDrivers:
    'Driving Application (For School Bus Drivers)',
  operationSystemForPrincipals: 'Operation System (For Principals)',
  leadership: 'Leadership',
  pressReleases: 'Press Releases',
  newsroom: 'Newsroom',
  customerStories: 'Customer Stories',
  helpCenter: 'Help Center',
  internetSafety: 'Internet Safety',
  home: 'Home',
  childSafetyProtection: 'Child Safety & Protection',
  termsOfUse: 'Terms of Use',
  privacyPolicies: 'Privacy Policies',
  parentsApplication: 'Parents Application',
  educatorsApplication: 'Educators Application',
  principalsManagement: 'Principals Management',
  incorrectFormat: 'Incorrect format',
  inviteYourSchool: 'Invite your school',
  seeItInAction: 'See it in action',
  yourChildSchoolName: 'Your child’s school name',
  schoolDirectorName: 'School Director name',
  schoolEmail: 'School Email',
  schoolHotline: 'School Hotline',
  schoolStudentQuantity: 'School Student Quantity',
  yourEmail: 'Your email',
  firstName: 'First Name',
  lastName: 'Last Name',
  email: 'Email',
  title: 'Title',
  plan: 'Plan',
  studentQuantity: 'Student Quantity',
  classroomsQuantity: 'Classrooms Quantity',
  scheduleDemo: 'Schedule Demo',
  demoMeeting: 'Demo meeting',
  getADemo: 'Get a demo',
  headquarterSouthOffice: 'Headquarter - South Office',
  northOffice: 'North Office',
  socialNetwork: 'Social Network',
  students: 'students',
  noExtraStudents: 'No Extra Students',
  extraStudent: 'Extra Student',
  availableModule: 'Available Module',
  features: 'Features',
  starter: 'Starter',
  tryItNow: 'Try it now',
  try: 'Try',
  enrollmentManagement: 'Enrollment Management',
  enrollmentFormManagement: 'Enrollment Form Management',
  websiteContentManagement: 'Website Content Management',
  studentsManagement: 'Students Management',
  parentsManagement: 'Parents Management',
  tuitionManagement: 'Tuition Management',
  classesManagement: 'Classes Management',
  staffsManagement: 'Staffs Management',
  payrollsManagement: 'Payrolls Management',
  transactionsManagement: 'Transactions Management',
  goodForSmallGroup: 'Good for small group',
  seed: 'Seed',
  curriculumsManagement: 'Curriculums Management',
  subjectsManagement: 'Subjects Management',
  badgesManagement: 'Badges Management',
  dailyMenusManagement: 'Daily Menus Management',
  photoAlbumsManagement: 'Photo Albums Management',
  eventsManagement: 'Events Management',
  checkedInManagement: 'Checked-in Management',
  leaveRequestsManagement: 'Leave Requests Management',
  bannersManagement: 'Banners Management',
  promotionsManagement: 'Promotions Management',
  notificationsManagement: 'Notifications Management',
  goodForMediumCenter: 'Good for medium center',
  bud: 'Bud',
  busesManagement: 'Buses Management',
  goodForBigKindergartenSchool: 'Good for big kindergarten school',
  leaf: 'Leaf',
  goodForHugePreSchoolCenter: 'Good for huge pre.school center',
  essentials: 'Essentials',
  maximumStudents: 'Maximum students',
  addMoreStudents: 'Add more students',
  onlineContentManagement: 'Online Content Management',
  schoolLandingPageWebsite: 'School Landing page Website',
  enrollmentOnlineForm: 'Enrollment Online Form',
  enrollmentOnlineFormManagement: 'Enrollment Online Form Management',
  engagementOperationApplications: 'Engagement & Operation Applications',
  parentsEngagementMobileApp: 'Parents Engagement Mobile App',
  teachersNanniesLeadingMobileApp: 'Teachers / Nannies Leading Mobile App',
  busDriversTrackingMobileApp: 'Bus Drivers Tracking Mobile App',
  principalsOperationManagementSystem:
    'Principals Operation Management System ',
  studentModuleManagementSystems: 'Student Module - Management Systems',
  enrollmentsManagement: 'Enrollments Management',
  tuitionsManagement: 'Tuitions Management',
  schoolModuleManagementSystems: 'School Module - Management Systems',
  schoolEventsManagement: 'School Events Management',
  visualizedDashboard: 'Visualized Dashboard',
  staffModuleManagementSystems: 'Staff Module - Management Systems',
  communicationModuleManagementSystems:
    'Communication Module - Management Systems',
  socialPostsManagement: 'Social Posts Management',
  integrationIoTSmartDevices: 'Integration IoT Smart Devices',
  aiCheckInCameras: 'AI Check-in Cameras',
  bluetoothThermometerDevices: 'Bluetooth Thermometer Devices',
  wirelessPaymentDevices: 'Wireless Payment Devices',
  billPrinters: 'Bill Printers',
  qrBarcodeScanner: 'QR / Barcode Scanner',
  support: 'Support',
  saleSupports: 'Sale Supports',
  consumerSupport247: 'Consumer Support 24/7',
  homePageText: 'Home page',
  saleProposal: 'Sale Proposal',
  personalInformation: 'Personal Information',
  centerInformation: 'Center Information',
  demoSchedule: 'Demo Schedule',
  sendSuccessfully: 'Send Successfully!',
  next: 'Next',
  sendRequest: 'Send request',
  scheduleDemoCheckboxLabel:
    'I wanna learn about the applications and management system',
  selectYourDate: 'Select your date',
  time: 'Time',
  demoMeetingType: 'Demo meeting',
  meetingOnline: 'Meeting online',
  meetingTypeMessage:
    'We only provide demo meeting in person at some limited cities',
  MCLScheduleDemoMeeting: 'Mầm Chồi Lá Schedule Demo Meeting',
  mins: 'mins',
  completeYourRequest: 'Complete Your Request',
  updatedOn: 'Updated on',
  city: 'City',
  district: 'District',
  ward: 'Ward',
  searchNews: 'Search News...',
  copied: 'Copied',
  relatedNews: 'Related News',
  newsroomDetail: 'Newsroom Detail',
  shareOn: 'Share on',
  outstandingFeatures: 'Outstanding features of Mam Choi La',
  introduce: 'Introduce',
  functions: 'Functions',
  extraStudents: 'extra students',
  basic: 'Basic',
  advanced: 'Advanced',
  professional: 'Professional',
  manageAutomaticNotifications: 'Manage Automatic Notifications',
  manageDailyMenu: 'Manage Daily Menu',
  manageWeeklySchedule: 'Manage weekly schedule',
  manageBMI: 'Manage BMI physical health check',
  zaloZNSAutomaticMessages: 'Zalo ZNS automatic messages',
  onlinePayment: 'Online payment',
  downloadParentApp: 'Download Parent App',

  selectField: {
    schoolYear: 'Select School Year',
    nationality: 'Select Nationality',
    language: 'Select Language',
    ethnicGroup: 'Select Ethnic Group',
    sibling: 'Select Sibling',
    bloodType: 'Select Blood Type',
    allergy: 'Select Allergy',
    title: 'Select Title',
    relationship: 'Select Relationship',
    city: 'Select City',
    district: 'Select District',
    ward: 'Select Ward',
    country: 'Select Country',
    enrollment: 'Select Enrollment',
    classes: 'Select Classes',
  },

  ...error,
  ...homePage,
};
