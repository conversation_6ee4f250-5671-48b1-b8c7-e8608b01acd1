/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    colors: {
      primary: 'rgba(239, 64, 96, 1)',
      secondary: 'rgba(255, 239, 186, 1)',
      darkSecondary: 'rgba(255, 226, 88, 1)',
      lightSecondary: 'rgba(255, 251, 179, 1)',
      black: 'rgba(0, 0, 0, 1)',
      dark: 'rgba(88, 89, 91, 1)',
      white: 'rgba(255, 255, 255, 1)',
      gray: 'rgba(190, 190, 190, 1)',
      red: 'rgba(239, 64, 96, 1)',
      green: 'rgba(69, 185, 124, 1)',
      blue: 'rgba(33, 64, 154, 1)',
      natreul: 'rgba(218, 221, 231, 1)',
      light: 'rgba(253, 253, 253, 1)',
      yellow: 'rgba(255, 222, 47, 1)',
      transparent: 'transparent',
    },
    fontSize: {
      10: ['0.625rem', { lineHeight: '0.875rem' }],
      12: ['0.75rem', { lineHeight: '1.25rem' }],
      14: ['0.875rem', { lineHeight: '1.25rem' }],
      15: ['0.875rem', { lineHeight: '1.375rem' }],
      16: ['1rem', { lineHeight: '1.625rem' }],
      18: ['1.125rem', { lineHeight: '1.3rem' }],
      20: ['1.25rem', { lineHeight: '1.875rem' }],
      24: ['1.5rem', { lineHeight: '2rem' }],
      30: ['1.875rem', { lineHeight: '2.625rem' }],
      32: ['2rem', { lineHeight: '2.625rem' }],
      36: ['2.25rem', { lineHeight: '3rem' }],
      40: ['2.5rem', { lineHeight: '3.185rem' }],
      48: ['3rem', { lineHeight: '4.4rem' }],
      56: ['3.5rem', { lineHeight: '4.6rem' }],
      68: ['4.25rem', { lineHeight: '5rem' }],
      72: ['4.5rem', { lineHeight: '5.375rem' }],
      94: ['5.8rem', { lineHeight: '7rem' }],
    },
    screens: {
      '2xl': { max: '1535px' },
      // => @media (max-width: 1535px) { ... }

      xl: { max: '1279px' },
      // => @media (max-width: 1279px) { ... }

      lg: { max: '1023px' },
      // => @media (max-width: 1023px) { ... }

      md: { max: '767px' },
      // => @media (max-width: 767px) { ... }

      sm: { max: '639px' },
      // => @media (max-width: 639px) { ... }

      'min-xl': { min: '1280px' },
      // => @media (min-width: 1280px) { ... }

      'min-lg': { min: '1024px' },
      // => @media (min-width: 1024px) { ... }

      'min-md': { min: '768px' },
      // => @media (min-width: 768px) { ... }

      'min-sm': { min: '640px' },
      // => @media (min-width: 640px) { ... }
    },
    extend: {
      spacing: {
        21: '5.25rem',
      },
      width: {},
      minWidth: {},
      height: {
        screen: '100vh',
      },
      minHeight: {
        screen: '100vh',
      },
      maxHeight: {
        screen: '100vh',
      },
      borderWidth: {
        1: '1px',
      },
      fontFamily: {
        barlow: ['var(--font-barlow)', 'sans-serif'],
        'GoogleSans-Bold': ['GoogleSans-Bold', 'sans-serif'],
        'GoogleSans-BoldItalic': ['GoogleSans-BoldItalic', 'sans-serif'],
        'GoogleSans-Italic': ['GoogleSans-Italic', 'sans-serif'],
        'GoogleSans-Medium': ['GoogleSans-Medium', 'sans-serif'],
        'GoogleSans-MediumItalic': ['GoogleSans-MediumItalic', 'sans-serif'],
        'GoogleSans-Regular': ['GoogleSans-Regular', 'sans-serif'],
      },
      fontWeight: {
        bold: '700',
        medium: '500',
        normal: '400',
      },
      boxShadow: {
        normal: '0 32px 64px -24px rgba(0,0,0,0.2)',
      },
      backgroundImage: {
        primaryGradient: 'linear-gradient(192.05deg, #FFDDE1 0%, #EE9CA7 100%)',
        secondaryGradient:
          'linear-gradient(192.05deg, #FFFFFF 0%, #FFEFBA 100%)',
        header:
          'linear-gradient(180deg,#FFFCF0,hsl(48deg 100% 97.06%) 40%,hsl(48deg 100% 97% / 90%) 60%,hsla(0,0%,100%,0))',
      },
      keyframes: {
        fadeInDown: {
          '0%': {
            opacity: '0',
            transform: 'translateY(-20px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          }
        }
      },
      animation: {
        fadeInDown: 'fadeInDown 0.3s ease-out'
      }
    },
  },
  plugins: [],
};
